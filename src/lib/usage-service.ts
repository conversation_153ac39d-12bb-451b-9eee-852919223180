"use client";

import { db, doc, getDoc, setDoc, updateDoc, increment, serverTimestamp } from './firebase';
import { useAuth } from './auth-context';
import { useCallback } from 'react';

// Define available subscription tiers
export type SubscriptionTier = 'FREE' | 'BASIC' | 'PRO';

// Usage limits per subscription tier
export const USAGE_LIMITS = {
  FREE: {
    DAILY_UPLOADS: 3,
    MONTHLY_UPLOADS: 15,
  },
  BASIC: {
    DAILY_UPLOADS: 10,
    MONTHLY_UPLOADS: 50,
  },
  PRO: {
    DAILY_UPLOADS: 30,
    MONTHLY_UPLOADS: 200,
  }
};

export type UsageData = {
  dailyUploads: number;
  monthlyUploads: number;
  lastUploadAt: Date | null;
  lastResetAt: Date | null;
  tier?: SubscriptionTier;
};

// Helper function to check if it's a new day
function isNewDay(date1: Date, date2: Date): boolean {
  return date1.getDate() !== date2.getDate() || 
         date1.getMonth() !== date2.getMonth() || 
         date1.getFullYear() !== date2.getFullYear();
}

// Helper function to check if it's a new month
function isNewMonth(date1: Date, date2: Date): boolean {
  return date1.getMonth() !== date2.getMonth() || 
         date1.getFullYear() !== date2.getFullYear();
}

// Check if user has exceeded their daily or monthly limits
export async function checkUserLimits(userId: string): Promise<{
  canUpload: boolean;
  reason?: string;
  currentUsage: UsageData;
}> {
  const userUsageRef = doc(db, 'userUsage', userId);
  const userUsageDoc = await getDoc(userUsageRef);
  
  // If no usage record exists, user can upload
  if (!userUsageDoc.exists()) {
    return { 
      canUpload: true,
      currentUsage: {
        dailyUploads: 0,
        monthlyUploads: 0,
        lastUploadAt: null,
        lastResetAt: null,
        tier: 'FREE'
      }
    };
  }
  
  const data = userUsageDoc.data();
  const now = new Date();
  const lastUploadAt = data.lastUploadAt?.toDate() || null;
  const lastResetAt = data.lastResetAt?.toDate() || null;
  const tier = data.tier || 'FREE';
  
  // Reset daily count if it's a new day
  let dailyUploads = data.dailyUploads || 0;
  if (lastUploadAt && isNewDay(lastUploadAt, now)) {
    dailyUploads = 0;
  }
  
  // Reset monthly count if it's a new month
  let monthlyUploads = data.monthlyUploads || 0;
  if (lastUploadAt && isNewMonth(lastUploadAt, now)) {
    monthlyUploads = 0;
  }
  
  const currentUsage: UsageData = {
    dailyUploads,
    monthlyUploads,
    lastUploadAt,
    lastResetAt,
    tier
  };
  
  // Check limits based on user's tier
  const tierLimits = USAGE_LIMITS[tier as keyof typeof USAGE_LIMITS];
  
  if (dailyUploads >= tierLimits.DAILY_UPLOADS) {
    return {
      canUpload: false,
      reason: `You've reached your daily limit of ${tierLimits.DAILY_UPLOADS} uploads for your ${tier} plan. Try again tomorrow or upgrade your plan.`,
      currentUsage
    };
  }
  
  if (monthlyUploads >= tierLimits.MONTHLY_UPLOADS) {
    return {
      canUpload: false,
      reason: `You've reached your monthly limit of ${tierLimits.MONTHLY_UPLOADS} uploads for your ${tier} plan. Upgrade your plan for more uploads.`,
      currentUsage
    };
  }
  
  return { canUpload: true, currentUsage };
}

// Track a successful upload
export async function trackUpload(userId: string): Promise<void> {
  const userUsageRef = doc(db, 'userUsage', userId);
  const userUsageDoc = await getDoc(userUsageRef);
  const now = new Date();
  
  if (!userUsageDoc.exists()) {
    // Create new usage record
    await setDoc(userUsageRef, {
      dailyUploads: 1,
      monthlyUploads: 1,
      lastUploadAt: serverTimestamp(),
      lastResetAt: serverTimestamp(),
      tier: 'FREE'
    });
    return;
  }
  
  const data = userUsageDoc.data();
  const lastUploadAt = data.lastUploadAt?.toDate() || null;
  
  // Determine if we need to reset counters
  const resetDaily = lastUploadAt && isNewDay(lastUploadAt, now);
  const resetMonthly = lastUploadAt && isNewMonth(lastUploadAt, now);
  
  if (resetDaily && resetMonthly) {
    // New day and new month - reset both counters
    await updateDoc(userUsageRef, {
      dailyUploads: 1,
      monthlyUploads: 1,
      lastUploadAt: serverTimestamp(),
      lastResetAt: serverTimestamp()
    });
  } else if (resetDaily) {
    // Only new day - reset daily counter and increment monthly
    await updateDoc(userUsageRef, {
      dailyUploads: 1,
      monthlyUploads: increment(1),
      lastUploadAt: serverTimestamp(),
      lastResetAt: serverTimestamp()
    });
  } else if (resetMonthly) {
    // Only new month - reset monthly counter and increment daily
    await updateDoc(userUsageRef, {
      dailyUploads: increment(1),
      monthlyUploads: 1, 
      lastUploadAt: serverTimestamp(),
      lastResetAt: serverTimestamp()
    });
  } else {
    // Same day and month - increment both counters
    await updateDoc(userUsageRef, {
      dailyUploads: increment(1),
      monthlyUploads: increment(1),
      lastUploadAt: serverTimestamp()
    });
  }
}

// Update user's subscription tier
export async function updateUserTier(userId: string, tier: SubscriptionTier): Promise<void> {
  const userUsageRef = doc(db, 'userUsage', userId);
  const userDoc = await getDoc(userUsageRef);
  
  if (!userDoc.exists()) {
    // Create new usage record with tier
    await setDoc(userUsageRef, {
      dailyUploads: 0,
      monthlyUploads: 0,
      lastUploadAt: null,
      lastResetAt: serverTimestamp(),
      tier
    });
  } else {
    // Update existing user's tier
    await updateDoc(userUsageRef, {
      tier
    });
  }
}

// Get user's current tier
export async function getUserTier(userId: string): Promise<SubscriptionTier> {
  const userUsageRef = doc(db, 'userUsage', userId);
  const userDoc = await getDoc(userUsageRef);
  
  if (!userDoc.exists()) {
    return 'FREE';
  }
  
  return userDoc.data().tier || 'FREE';
}

// React hook for using the usage service
export function useUsageService() {
  const { user } = useAuth();
  
  const checkLimits = useCallback(async () => {
    if (!user) {
      return {
        canUpload: false,
        reason: "Please sign in to upload files",
        currentUsage: {
          dailyUploads: 0,
          monthlyUploads: 0,
          lastUploadAt: null,
          lastResetAt: null,
          tier: 'FREE'
        }
      };
    }

    return checkUserLimits(user.id);
  }, [user?.id]); // Only depend on user.id, not the entire user object
  
  const trackUserUpload = useCallback(async () => {
    if (!user) {
      throw new Error('User must be logged in to track usage');
    }

    return trackUpload(user.id);
  }, [user?.id]); // Only depend on user.id, not the entire user object
  
  const updateTier = useCallback(async (tier: SubscriptionTier) => {
    if (!user) {
      throw new Error('User must be logged in to update tier');
    }

    return updateUserTier(user.id, tier);
  }, [user?.id]); // Only depend on user.id, not the entire user object
  
  const getTier = useCallback(async () => {
    if (!user) {
      return 'FREE' as SubscriptionTier;
    }

    return getUserTier(user.id);
  }, [user?.id]); // Only depend on user.id, not the entire user object
  
  return {
    checkLimits,
    trackUserUpload,
    updateTier,
    getTier,
    LIMITS: USAGE_LIMITS
  };
} 