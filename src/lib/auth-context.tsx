"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';
import { 
  auth, 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword, 
  signOut, 
  onAuthStateChanged,
  User as FirebaseUser,
  db,
  doc,
  getDoc,
  GoogleAuthProvider,
  signInWithPopup
} from './firebase';
import { updateProfile, sendPasswordResetEmail } from 'firebase/auth';
import { logger } from '@/lib/logger';
import type { SubscriptionTier } from './usage-service';

type User = {
  id: string;
  email: string | null;
  name?: string | null;
  tier?: SubscriptionTier;
};

type AuthContextType = {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string, name?: string) => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Load user tier information
  const loadUserTier = async (userId: string) => {
    try {
      const userUsageRef = doc(db, 'userUsage', userId);
      const userUsageDoc = await getDoc(userUsageRef);
      
      if (userUsageDoc.exists()) {
        const tier = userUsageDoc.data().tier || 'FREE';
        return tier as SubscriptionTier;
      }
      
      return 'FREE' as SubscriptionTier;
    } catch (error) {
      logger.error('Error loading user tier:', error);
      return 'FREE' as SubscriptionTier;
    }
  };

  // Listen for auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        // Load user tier when user is authenticated
        const tier = await loadUserTier(firebaseUser.uid);
        
        const user: User = {
          id: firebaseUser.uid,
          email: firebaseUser.email,
          name: firebaseUser.displayName,
          tier
        };
        setUser(user);
      } else {
        setUser(null);
      }
      setLoading(false);
    });

    // Cleanup subscription
    return () => unsubscribe();
  }, []);

  // Login with email and password
  const login = async (email: string, password: string) => {
    setLoading(true);
    
    try {
      await signInWithEmailAndPassword(auth, email, password);
    } finally {
      setLoading(false);
    }
  };

  // Signup with email and password
  const signup = async (email: string, password: string, name?: string) => {
    setLoading(true);
    
    try {
      const result = await createUserWithEmailAndPassword(auth, email, password);
      
      // Set display name if provided
      if (name && result.user) {
        await updateProfile(result.user, {
          displayName: name
        });
        
        // Update local user state to include name
        setUser(prev => prev ? {...prev, name} : null);
      }
    } finally {
      setLoading(false);
    }
  };

  // Logout
  const logout = async () => {
    setLoading(true);
    
    try {
      await signOut(auth);
    } finally {
      setLoading(false);
    }
  };

  // Reset password
  const resetPassword = async (email: string) => {
    setLoading(true);
    try {
      await sendPasswordResetEmail(auth, email);
    } finally {
      setLoading(false);
    }
  };

  // Login with Google
  const loginWithGoogle = async () => {
    setLoading(true);
    try {
      const provider = new GoogleAuthProvider();
      await signInWithPopup(auth, provider);
      // onAuthStateChanged will handle user state
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, login, signup, logout, resetPassword, loginWithGoogle }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 