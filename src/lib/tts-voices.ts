import { logger } from '@/lib/logger';

// Available voice options from ElevenLabs premade voice library
// Curated selection of 14 voices (7 male, 7 female) optimized for educational content
export const AVAILABLE_TTS_VOICES = [
  // Male voices (7)
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>',
  // Female voices (7)
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
] as const;

export type TTSVoiceName = typeof AVAILABLE_TTS_VOICES[number];

// Default voice settings
export const DEFAULT_TTS_VOICES = {
  questionVoice: '<PERSON>' as TTSVoiceName,
  answerVoice: '<PERSON>' as TTSVoiceName,
} as const;

// Voice descriptions for better user experience
// Based on actual ElevenLabs premade voice characteristics
export const VOICE_DESCRIPTIONS: Record<TTSVoiceName, string> = {
  // Male voices
  'Drew': 'Well-rounded male voice, American accent - ideal for news and education',
  '<PERSON>': 'Authoritative male voice, American accent - professional and clear',
  '<PERSON>': 'Formal male voice, British accent - perfect for educational content',
  '<PERSON>': 'Calm male voice, American accent - soothing for meditation and study',
  '<PERSON><PERSON>': 'Well-rounded male voice, American accent - versatile for narration',
  'George': 'Mature male voice, British accent - distinctive for storytelling',
  'Arnold': 'Crisp male voice, American accent - clear pronunciation for learning',
  // Female voices
  'Rachel': 'Calm female voice, American accent - soothing and clear for narration',
  'Aria': 'Husky female voice, American accent - specifically designed for education',
  'Sarah': 'Professional female voice, American accent - engaging and clear',
  'Matilda': 'Upbeat female voice, American accent - perfect for educational content',
  'Emily': 'Calm female voice, American accent - gentle and meditative',
  'Lily': 'Warm female voice, British accent - confident and engaging',
  'Dorothy': 'Pleasant female voice, British accent - friendly and clear'
};

// Voice metadata including gender, accent, and preview URLs
export interface VoiceMetadata {
  name: TTSVoiceName;
  gender: 'male' | 'female';
  accent: string;
  description: string;
  age: string;
  use_case: string;
  preview_url: string;
  voice_id: string;
}

// Complete voice metadata for the curated selection
export const VOICE_METADATA: Record<TTSVoiceName, VoiceMetadata> = {
  // Male voices
  'Drew': {
    name: 'Drew',
    gender: 'male',
    accent: 'American',
    description: 'Well-rounded',
    age: 'Middle-aged',
    use_case: 'News & Education',
    preview_url: 'https://storage.googleapis.com/eleven-public-prod/premade/voices/29vD33N1CtxCmqQRPOHJ/b99fc51d-12d3-4312-b480-a8a45a7d51ef.mp3',
    voice_id: '29vD33N1CtxCmqQRPOHJ'
  },
  'Paul': {
    name: 'Paul',
    gender: 'male',
    accent: 'American',
    description: 'Authoritative',
    age: 'Middle-aged',
    use_case: 'News & Professional',
    preview_url: 'https://storage.googleapis.com/eleven-public-prod/premade/voices/5Q0t7uMcjvnagumLfvZi/a4aaa30e-54c4-44a4-8e46-b9b00505d963.mp3',
    voice_id: '5Q0t7uMcjvnagumLfvZi'
  },
  'Daniel': {
    name: 'Daniel',
    gender: 'male',
    accent: 'British',
    description: 'Formal',
    age: 'Middle-aged',
    use_case: 'Educational & Informative',
    preview_url: 'https://storage.googleapis.com/eleven-public-prod/premade/voices/onwK4e9ZLuTAKqWW03F9/7eee0236-1a72-4b86-b303-5dcadc007ba9.mp3',
    voice_id: 'onwK4e9ZLuTAKqWW03F9'
  },
  'Thomas': {
    name: 'Thomas',
    gender: 'male',
    accent: 'American',
    description: 'Calm',
    age: 'Young',
    use_case: 'Meditation & Study',
    preview_url: 'https://storage.googleapis.com/eleven-public-prod/premade/voices/GBv7mTt0atIp3Br8iCZE/98542988-5267-4148-9a9e-baa8c4f14644.mp3',
    voice_id: 'GBv7mTt0atIp3Br8iCZE'
  },
  'Antoni': {
    name: 'Antoni',
    gender: 'male',
    accent: 'American',
    description: 'Well-rounded',
    age: 'Young',
    use_case: 'Narration & Versatile',
    preview_url: 'https://storage.googleapis.com/eleven-public-prod/premade/voices/ErXwobaYiN019PkySvjV/2d5ab2a3-4578-470f-b797-6331e46a7d55.mp3',
    voice_id: 'ErXwobaYiN019PkySvjV'
  },
  'George': {
    name: 'George',
    gender: 'male',
    accent: 'British',
    description: 'Mature',
    age: 'Middle-aged',
    use_case: 'Storytelling & Narrative',
    preview_url: 'https://storage.googleapis.com/eleven-public-prod/premade/voices/JBFqnCBsd6RMkjVDRZzb/e6206d1a-0721-4787-aafb-06a6e705cac5.mp3',
    voice_id: 'JBFqnCBsd6RMkjVDRZzb'
  },
  'Arnold': {
    name: 'Arnold',
    gender: 'male',
    accent: 'American',
    description: 'Crisp',
    age: 'Middle-aged',
    use_case: 'Narration & Learning',
    preview_url: 'https://storage.googleapis.com/eleven-public-prod/premade/voices/VR6AewLTigWG4xSOukaG/49a22885-80d5-48e8-87a3-076fc9193d9a.mp3',
    voice_id: 'VR6AewLTigWG4xSOukaG'
  },
  // Female voices
  'Rachel': {
    name: 'Rachel',
    gender: 'female',
    accent: 'American',
    description: 'Calm',
    age: 'Young',
    use_case: 'Narration & Soothing',
    preview_url: 'https://storage.googleapis.com/eleven-public-prod/premade/voices/21m00Tcm4TlvDq8ikWAM/b4928a68-c03b-411f-8533-3d5c299fd451.mp3',
    voice_id: '21m00Tcm4TlvDq8ikWAM'
  },
  'Aria': {
    name: 'Aria',
    gender: 'female',
    accent: 'American',
    description: 'Husky',
    age: 'Middle-aged',
    use_case: 'Educational & Informative',
    preview_url: 'https://storage.googleapis.com/eleven-public-prod/premade/voices/9BWtsMINqrJLrRacOk9x/405766b8-1f4e-4d3c-aba1-6f25333823ec.mp3',
    voice_id: '9BWtsMINqrJLrRacOk9x'
  },
  'Sarah': {
    name: 'Sarah',
    gender: 'female',
    accent: 'American',
    description: 'Professional',
    age: 'Young',
    use_case: 'Entertainment & Professional',
    preview_url: 'https://storage.googleapis.com/eleven-public-prod/premade/voices/EXAVITQu4vr4xnSDxMaL/01a3e33c-6e99-4ee7-8543-ff2216a32186.mp3',
    voice_id: 'EXAVITQu4vr4xnSDxMaL'
  },
  'Matilda': {
    name: 'Matilda',
    gender: 'female',
    accent: 'American',
    description: 'Upbeat',
    age: 'Middle-aged',
    use_case: 'Educational & Informative',
    preview_url: 'https://storage.googleapis.com/eleven-public-prod/premade/voices/XrExE9yKIg1WjnnlVkGX/b930e18d-6b4d-466e-bab2-0ae97c6d8535.mp3',
    voice_id: 'XrExE9yKIg1WjnnlVkGX'
  },
  'Emily': {
    name: 'Emily',
    gender: 'female',
    accent: 'American',
    description: 'Calm',
    age: 'Young',
    use_case: 'Meditation & Gentle',
    preview_url: 'https://storage.googleapis.com/eleven-public-prod/premade/voices/LcfcDJNUP1GQjkzn1xUU/e4b994b7-9713-4238-84f3-add8fccaaccd.mp3',
    voice_id: 'LcfcDJNUP1GQjkzn1xUU'
  },
  'Lily': {
    name: 'Lily',
    gender: 'female',
    accent: 'British',
    description: 'Warm & Confident',
    age: 'Middle-aged',
    use_case: 'Narration & Engaging',
    preview_url: 'https://storage.googleapis.com/eleven-public-prod/premade/voices/pFZP5JQG7iQjIQuC4Bku/89b68b35-b3dd-4348-a84a-a3c13a3c2b30.mp3',
    voice_id: 'pFZP5JQG7iQjIQuC4Bku'
  },
  'Dorothy': {
    name: 'Dorothy',
    gender: 'female',
    accent: 'British',
    description: 'Pleasant',
    age: 'Young',
    use_case: 'Narration & Friendly',
    preview_url: 'https://storage.googleapis.com/eleven-public-prod/premade/voices/ThT5KcBeYPX3keUQqHPh/981f0855-6598-48d2-9f8f-b6d92fbbe3fc.mp3',
    voice_id: 'ThT5KcBeYPX3keUQqHPh'
  }
};

/**
 * Validates if a voice name is available in our ElevenLabs voice selection
 */
export function isValidTTSVoice(voice: string): voice is TTSVoiceName {
  return AVAILABLE_TTS_VOICES.includes(voice as TTSVoiceName);
}

/**
 * Gets a safe voice name, falling back to default if invalid
 */
export function getSafeVoiceName(voice: string | undefined, defaultVoice: TTSVoiceName): TTSVoiceName {
  if (!voice || !isValidTTSVoice(voice)) {
    return defaultVoice;
  }
  return voice;
}

/**
 * Fetches pre-recorded audio file from Vercel Blob storage for voice previews
 * @param voiceName The voice name to fetch audio for
 * @param type Either 'question' or 'answer' for the sample type
 * @returns Promise<string> URL of the audio file
 */
export async function fetchPrerecordedAudio(voiceName: TTSVoiceName, type: 'question' | 'answer'): Promise<string> {
  try {
    // Construct the blob path based on the naming convention
    const blobPath = `tts-settings/${voiceName}-${type}`;

    // Fetch the list of blobs to find the exact file
    const response = await fetch('/api/blob-list', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ prefix: blobPath }),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch blob list: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || 'Failed to list blobs');
    }

    // Find the matching blob
    const matchingBlob = data.blobs?.find((blob: any) =>
      blob.pathname.startsWith(blobPath) && blob.pathname.endsWith('.wav')
    );

    if (!matchingBlob) {
      throw new Error(`Pre-recorded audio not found for voice: ${voiceName} (${type}). Expected path: ${blobPath}.wav`);
    }

    logger.log(`[TTS Voices] Found pre-recorded audio for ${voiceName} (${type}):`, matchingBlob.url);
    return matchingBlob.url;
  } catch (error) {
    logger.error(`Error fetching pre-recorded audio for ${voiceName} (${type}):`, error);
    throw error;
  }
}
