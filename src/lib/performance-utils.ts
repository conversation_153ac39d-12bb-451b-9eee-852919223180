import { FlashcardData } from '@/types';
import { logger } from './logger';

// Performance thresholds
export const PERFORMANCE_THRESHOLDS = {
  SMALL_SET: 20,      // No optimizations needed
  MEDIUM_SET: 50,     // Light optimizations
  LARGE_SET: 100,     // Heavy optimizations
  VERY_LARGE_SET: 200 // Maximum optimizations
} as const;

// Determine performance tier based on flashcard count
export function getPerformanceTier(count: number): keyof typeof PERFORMANCE_THRESHOLDS {
  if (count <= PERFORMANCE_THRESHOLDS.SMALL_SET) return 'SMALL_SET';
  if (count <= PERFORMANCE_THRESHOLDS.MEDIUM_SET) return 'MEDIUM_SET';
  if (count <= PERFORMANCE_THRESHOLDS.LARGE_SET) return 'LARGE_SET';
  return 'VERY_LARGE_SET';
}

// Check if virtualization should be used
export function shouldUseVirtualization(count: number): boolean {
  return count > PERFORMANCE_THRESHOLDS.MEDIUM_SET;
}

// Check if lazy loading should be used
export function shouldUseLazyLoading(count: number): boolean {
  return count > PERFORMANCE_THRESHOLDS.LARGE_SET;
}

// Debounce function for performance optimization
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle function for performance optimization
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Batch process flashcards for better performance
export function batchProcess<T>(
  items: T[],
  batchSize: number,
  processor: (batch: T[]) => void,
  delay: number = 0
): Promise<void> {
  return new Promise((resolve) => {
    let index = 0;
    
    function processBatch() {
      const batch = items.slice(index, index + batchSize);
      if (batch.length === 0) {
        resolve();
        return;
      }
      
      processor(batch);
      index += batchSize;
      
      if (delay > 0) {
        setTimeout(processBatch, delay);
      } else {
        // Use requestAnimationFrame for smooth processing
        requestAnimationFrame(processBatch);
      }
    }
    
    processBatch();
  });
}

// Optimize flashcard data for memory efficiency
export function optimizeFlashcardData(flashcards: FlashcardData[]): FlashcardData[] {
  return flashcards.map((card, index) => ({
    ...card,
    id: card.id ?? index, // Ensure ID exists for efficient tracking
    // Trim whitespace to save memory
    question: card.question.trim(),
    answer: card.answer.trim()
  }));
}

// Memory usage estimation
export function estimateMemoryUsage(flashcards: FlashcardData[]): number {
  const avgQuestionLength = flashcards.reduce((sum, card) => sum + card.question.length, 0) / flashcards.length;
  const avgAnswerLength = flashcards.reduce((sum, card) => sum + card.answer.length, 0) / flashcards.length;
  
  // Rough estimation: 2 bytes per character + object overhead
  const avgCardSize = (avgQuestionLength + avgAnswerLength) * 2 + 100; // 100 bytes overhead
  const totalBytes = avgCardSize * flashcards.length;
  
  return totalBytes / (1024 * 1024); // Convert to MB
}

// Performance monitoring utilities
export class PerformanceTracker {
  private static instance: PerformanceTracker;
  private metrics: Map<string, number[]> = new Map();

  static getInstance(): PerformanceTracker {
    if (!PerformanceTracker.instance) {
      PerformanceTracker.instance = new PerformanceTracker();
    }
    return PerformanceTracker.instance;
  }

  startTiming(operation: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (!this.metrics.has(operation)) {
        this.metrics.set(operation, []);
      }
      
      this.metrics.get(operation)!.push(duration);
      
      // Log slow operations
      if (duration > 100) {
        logger.warn(`[Performance] Slow operation: ${operation} took ${duration.toFixed(2)}ms`);
      }
    };
  }

  getAverageTime(operation: string): number {
    const times = this.metrics.get(operation);
    if (!times || times.length === 0) return 0;
    
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }

  getMetrics(): Record<string, { average: number; count: number; max: number }> {
    const result: Record<string, { average: number; count: number; max: number }> = {};
    
    for (const [operation, times] of this.metrics.entries()) {
      result[operation] = {
        average: this.getAverageTime(operation),
        count: times.length,
        max: Math.max(...times)
      };
    }
    
    return result;
  }

  reset(): void {
    this.metrics.clear();
  }
}

// Hook for performance optimization recommendations
export function getPerformanceRecommendations(flashcardCount: number): string[] {
  const recommendations: string[] = [];
  const tier = getPerformanceTier(flashcardCount);
  
  switch (tier) {
    case 'VERY_LARGE_SET':
      recommendations.push('Consider splitting large flashcard sets into smaller chunks');
      recommendations.push('Use pagination or infinite scrolling for better performance');
      // Fall through
    case 'LARGE_SET':
      recommendations.push('Virtualization is enabled for optimal scrolling performance');
      recommendations.push('TTS components are lazy-loaded to reduce initial bundle size');
      // Fall through
    case 'MEDIUM_SET':
      recommendations.push('List rendering is optimized with React.memo');
      // Fall through
    case 'SMALL_SET':
      recommendations.push('Performance monitoring is active');
      break;
  }
  
  const memoryUsage = estimateMemoryUsage(Array(flashcardCount).fill({ question: 'Sample question', answer: 'Sample answer' }));
  if (memoryUsage > 10) {
    recommendations.push(`High memory usage detected (~${memoryUsage.toFixed(1)}MB). Consider data optimization.`);
  }
  
  return recommendations;
}
