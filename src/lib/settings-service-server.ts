import { adminDb } from './firebase-admin';
import { FieldValue } from 'firebase-admin/firestore';
import type { UserSettings } from '@/types';
import { DEFAULT_TTS_VOICES, isValidTTSVoice } from './tts-voices';
import { decryptApiKey, encryptApiKey, validateElevenLabsApiKeyFormat } from './api-key-utils';
import { logger } from '@/lib/logger';

const DEFAULT_SETTINGS: UserSettings = {
  answerDetail: 50,
  cardRange: {
    min: 30,
    max: 50
  },
  ttsVoices: DEFAULT_TTS_VOICES
};

/**
 * Server-side function to get user settings
 * @param userId The user ID
 * @returns Promise<UserSettings> The user's settings
 */
export async function getUserSettingsServer(userId: string): Promise<UserSettings> {
  try {
    const userDocRef = adminDb.collection('users').doc(userId);
    const userDoc = await userDocRef.get();

    if (!userDoc.exists) {
      // Return default settings if user document doesn't exist
      return DEFAULT_SETTINGS;
    }

    const data = userDoc.data();
    const settings = data?.settings || DEFAULT_SETTINGS;
    
    // Decrypt API key if present
    if (settings.elevenLabsApiKey) {
      try {
        settings.elevenLabsApiKey = decryptApiKey(settings.elevenLabsApiKey);
      } catch (error) {
        logger.error('Error decrypting API key:', error);
        // Remove invalid encrypted key
        delete settings.elevenLabsApiKey;
      }
    }
    
    return settings;
  } catch (error) {
    logger.error('Error fetching user settings:', error);
    return DEFAULT_SETTINGS;
  }
}

/**
 * Server-side function to get user's ElevenLabs API key
 * @param userId The user ID
 * @returns Promise<string | null> The decrypted API key or null if not found
 */
export async function getUserElevenLabsApiKey(userId: string): Promise<string | null> {
  try {
    const settings = await getUserSettingsServer(userId);
    return settings.elevenLabsApiKey || null;
  } catch (error) {
    logger.error('Error getting user ElevenLabs API key:', error);
    return null;
  }
}

/**
 * Validate settings object
 * @param settings The settings to validate
 * @returns Object with validation result
 */
function validateSettings(settings: Partial<UserSettings>): { valid: boolean; error?: string } {
  // Validate answerDetail if provided
  if ('answerDetail' in settings) {
    const detail = settings.answerDetail;
    if (typeof detail !== 'number' || detail < 0 || detail > 100) {
      return { valid: false, error: 'Answer detail must be a number between 0 and 100' };
    }
  }

  // Validate cardRange if provided
  if ('cardRange' in settings) {
    const range = settings.cardRange;
    if (!range || typeof range !== 'object') {
      return { valid: false, error: 'Card range must be an object' };
    }
    if (typeof range.min !== 'number' || typeof range.max !== 'number') {
      return { valid: false, error: 'Card range min and max must be numbers' };
    }
    if (range.min < 1 || range.max < 1 || range.min > range.max) {
      return { valid: false, error: 'Invalid card range values' };
    }
  }

  // Validate ttsVoices if provided
  if ('ttsVoices' in settings) {
    const voices = settings.ttsVoices;
    if (voices && typeof voices === 'object') {
      if ('questionVoice' in voices && voices.questionVoice && !isValidTTSVoice(voices.questionVoice)) {
        return { valid: false, error: 'Invalid question voice selection' };
      }
      if ('answerVoice' in voices && voices.answerVoice && !isValidTTSVoice(voices.answerVoice)) {
        return { valid: false, error: 'Invalid answer voice selection' };
      }
    }
  }

  // Validate ElevenLabs API key if provided
  if ('elevenLabsApiKey' in settings) {
    const apiKey = settings.elevenLabsApiKey;
    if (apiKey && typeof apiKey === 'string') {
      if (!validateElevenLabsApiKeyFormat(apiKey)) {
        return { valid: false, error: 'Invalid ElevenLabs API key format' };
      }
    }
  }

  return { valid: true };
}

/**
 * Server-side function to update user settings
 * @param userId The user ID
 * @param settings The settings to update
 * @returns Promise<{success: boolean, error?: string}> Update result
 */
export async function updateUserSettingsServer(
  userId: string,
  settings: Partial<UserSettings>
): Promise<{ success: boolean; error?: string }> {
  try {
    // Validate settings
    const validation = validateSettings(settings);
    if (!validation.valid) {
      return { success: false, error: validation.error };
    }

    // Get current settings
    const currentSettings = await getUserSettingsServer(userId);

    // Merge current settings with updates
    const updatedSettings = {
      ...currentSettings,
      ...settings,
      cardRange: {
        ...currentSettings.cardRange,
        ...(settings.cardRange || {})
      },
      ttsVoices: {
        ...currentSettings.ttsVoices,
        ...(settings.ttsVoices || {})
      }
    };

    // Handle API key encryption or deletion
    logger.log('[Settings Server] API key processing:', {
      hasApiKeyInSettings: 'elevenLabsApiKey' in settings,
      incomingApiKey: settings.elevenLabsApiKey ? '[REDACTED]' : settings.elevenLabsApiKey,
      updatedApiKey: updatedSettings.elevenLabsApiKey ? '[REDACTED]' : updatedSettings.elevenLabsApiKey
    });

    if ('elevenLabsApiKey' in settings) {
      if (updatedSettings.elevenLabsApiKey && updatedSettings.elevenLabsApiKey.trim()) {
        // Encrypt the API key if provided
        logger.log('[Settings Server] Encrypting API key');
        try {
          updatedSettings.elevenLabsApiKey = encryptApiKey(updatedSettings.elevenLabsApiKey.trim());
          logger.log('[Settings Server] API key encrypted successfully');
        } catch (error) {
          logger.error('[Settings Server] Error encrypting API key:', error);
          return { success: false, error: 'Failed to encrypt API key' };
        }
      } else {
        // Delete the API key if empty using Firestore FieldValue.delete()
        logger.log('[Settings Server] Deleting API key from settings using FieldValue.delete()');
        updatedSettings.elevenLabsApiKey = FieldValue.delete() as any;
        logger.log('[Settings Server] API key marked for deletion in Firestore');
      }
    } else {
      logger.log('[Settings Server] No elevenLabsApiKey in incoming settings, skipping API key processing');
    }

    // Update in Firestore
    logger.log('[Settings Server] Final updatedSettings before Firestore update:', {
      ...updatedSettings,
      elevenLabsApiKey: updatedSettings.elevenLabsApiKey && typeof updatedSettings.elevenLabsApiKey === 'object' && 'isEqual' in updatedSettings.elevenLabsApiKey
        ? '[FIELD_DELETE]'
        : updatedSettings.elevenLabsApiKey
          ? '[REDACTED]'
          : updatedSettings.elevenLabsApiKey
    });

    const userDocRef = adminDb.collection('users').doc(userId);
    await userDocRef.set(
      { settings: updatedSettings },
      { merge: true }
    );

    logger.log('[Settings Server] Settings updated successfully in Firestore for user:', userId);
    return { success: true };

  } catch (error) {
    logger.error('Error updating user settings:', error);
    return { success: false, error: 'Failed to update settings' };
  }
}
