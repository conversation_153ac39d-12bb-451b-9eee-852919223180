"use client";

import { db, collection, addDoc, getDocs, query, where, orderBy, Timestamp, doc, deleteDoc, getDoc } from './firebase';
import type { FlashcardData } from '@/types';
import { useAuth } from './auth-context';
import { useCallback } from 'react';
import { logger } from '@/lib/logger';

export interface FlashcardSet {
  id: string;
  title: string;
  createdAt: Date;
  flashcards: FlashcardData[];
}

// Check if a flashcard set with the same title already exists
export async function checkDuplicateFlashcardSet(userId: string, title: string): Promise<boolean> {
  try {
    const flashcardsCollection = collection(db, 'flashcardSets');
    const q = query(
      flashcardsCollection, 
      where('userId', '==', userId),
      where('title', '==', title)
    );
    
    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  } catch (error) {
    logger.error('Error checking for duplicate flashcard set:', error);
    return false;
  }
}

// Save flashcards to Firestore
export async function saveFlashcards(userId: string, flashcards: FlashcardData[], title?: string) {
  try {
    const actualTitle = title || `Flashcards - ${new Date().toLocaleString()}`;
    
    // Check if a set with the same title already exists
    const isDuplicate = await checkDuplicateFlashcardSet(userId, actualTitle);
    if (isDuplicate) {
      return { 
        success: false, 
        error: 'A flashcard set with this title already exists in your history.',
        isDuplicate: true
      };
    }
    
    const flashcardsCollection = collection(db, 'flashcardSets');
    
    const docRef = await addDoc(flashcardsCollection, {
      userId,
      title: actualTitle,
      createdAt: Timestamp.now(),
      flashcards: flashcards.map(card => {
        const base = {
          question: card.question,
          answer: card.answer,
        };
        return card.id !== undefined ? { ...base, id: card.id } : base;
      })
    });
    
    return { success: true, id: docRef.id };
  } catch (error) {
    logger.error('Error saving flashcards:', error);
    return { success: false, error };
  }
}

// Delete a flashcard set
export async function deleteFlashcardSet(userId: string, setId: string) {
  try {
    // First, get the document to verify ownership
    const docRef = doc(db, 'flashcardSets', setId);
    const docSnap = await getDoc(docRef);
    
    if (!docSnap.exists()) {
      return { success: false, error: 'Flashcard set not found' };
    }
    
    // Verify this flashcard set belongs to the current user
    const data = docSnap.data();
    if (data.userId !== userId) {
      return { success: false, error: 'You do not have permission to delete this flashcard set' };
    }
    
    // Delete the document
    await deleteDoc(docRef);
    return { success: true };
  } catch (error) {
    logger.error('Error deleting flashcard set:', error);
    return { success: false, error };
  }
}

// Get user's flashcard sets from Firestore
export async function getUserFlashcardSets(userId: string): Promise<FlashcardSet[]> {
  try {
    const flashcardsCollection = collection(db, 'flashcardSets');
    const q = query(
      flashcardsCollection, 
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    
    const flashcardSets: FlashcardSet[] = querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        title: data.title,
        createdAt: data.createdAt.toDate(),
        flashcards: data.flashcards
      };
    });
    
    return flashcardSets;
  } catch (error) {
    logger.error('Error fetching flashcard sets:', error);
    return [];
  }
}

// Hook to use the flashcard service with authentication
export function useFlashcardService() {
  const { user } = useAuth();
  
  const saveUserFlashcards = useCallback(async (flashcards: FlashcardData[], title?: string) => {
    if (!user) {
      throw new Error('User must be logged in to save flashcards');
    }
    
    return saveFlashcards(user.id, flashcards, title);
  }, [user]);
  
  const getUserSets = useCallback(async (): Promise<FlashcardSet[]> => {
    if (!user) {
      return [];
    }
    
    return getUserFlashcardSets(user.id);
  }, [user]);

  const checkIfSetExists = useCallback(async (title: string): Promise<boolean> => {
    if (!user) {
      return false;
    }
    
    return checkDuplicateFlashcardSet(user.id, title);
  }, [user]);
  
  const deleteUserFlashcardSet = useCallback(async (setId: string) => {
    if (!user) {
      throw new Error('User must be logged in to delete flashcards');
    }
    
    return deleteFlashcardSet(user.id, setId);
  }, [user]);
  
  return {
    saveUserFlashcards,
    getUserSets,
    checkIfSetExists,
    deleteUserFlashcardSet
  };
} 