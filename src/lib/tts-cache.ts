import crypto from 'crypto';
import { put, head, del } from '@vercel/blob';
import { logger } from './logger';

// Cache configuration
export const CACHE_CONFIG = {
  PREFIX: 'tts-cache',
  EXPIRY_DAYS: 30,
  MAX_TEXT_LENGTH: 5000, // Maximum text length to cache
  CONTENT_TYPE: 'audio/mpeg' // MP3 format from ElevenLabs
} as const;

/**
 * Generate a deterministic cache key for TTS audio
 * @param text The text content to be spoken
 * @param voiceName The voice name to use
 * @returns A hash-based cache key
 */
export function generateCacheKey(text: string, voiceName: string): string {
  // Normalize text by trimming whitespace and converting to lowercase for consistent caching
  const normalizedText = text.trim().toLowerCase();

  // Don't cache very long texts to avoid storage bloat
  if (normalizedText.length > CACHE_CONFIG.MAX_TEXT_LENGTH) {
    throw new Error(`Text too long for caching (max ${CACHE_CONFIG.MAX_TEXT_LENGTH} characters)`);
  }

  // Create content string with text and voice
  const content = `${normalizedText}|${voiceName.toLowerCase()}`;

  // Create SHA-256 hash for deterministic key generation
  const hash = crypto.createHash('sha256').update(content, 'utf8').digest('hex');

  // Use first 16 characters of hash for shorter, manageable keys
  const shortHash = hash.substring(0, 16);

  return `${CACHE_CONFIG.PREFIX}/${shortHash}.mp3`;
}

/**
 * Check if cached audio exists in Vercel Blob storage
 * @param cacheKey The cache key to check
 * @returns Promise<{exists: boolean, url?: string}> indicating if cache exists and its URL
 */
export async function checkCacheExists(cacheKey: string): Promise<{exists: boolean, url?: string}> {
  try {
    logger.log('[TTS Cache] Checking cache existence for key:', cacheKey);

    // Since Vercel Blob adds unique suffixes to filenames, we need to search by prefix
    // Extract the base filename without extension to use as prefix
    const baseKey = cacheKey.replace('.mp3', '');

    const { list } = await import('@vercel/blob');
    const { blobs } = await list({
      prefix: baseKey,
      limit: 10 // Should only be one file per cache key
    });

    // Look for a blob that starts with our cache key
    const matchingBlob = blobs.find(blob =>
      blob.pathname.startsWith(baseKey) && blob.pathname.endsWith('.mp3')
    );

    if (matchingBlob) {
      logger.log('[TTS Cache] Cache hit for key:', cacheKey);
      logger.log('[TTS Cache] Found cached file:', matchingBlob.pathname);
      return { exists: true, url: matchingBlob.url };
    } else {
      logger.log('[TTS Cache] Cache miss for key:', cacheKey);
      return { exists: false };
    }
  } catch (error) {
    logger.log('[TTS Cache] Cache check failed for key:', cacheKey, error);
    return { exists: false };
  }
}

// Note: convertPCMToWAV function removed as it's no longer needed
// ElevenLabs returns MP3 data directly, which we store and use as-is

/**
 * Store audio data in Vercel Blob storage
 * @param cacheKey The cache key to store under
 * @param audioData Base64 encoded audio data from ElevenLabs (MP3 format)
 * @returns Promise<string> URL of the stored blob
 */
export async function storeAudioInCache(cacheKey: string, audioData: string): Promise<string> {
  try {
    logger.log('[TTS Cache] Storing audio in cache with key:', cacheKey);

    // ElevenLabs returns MP3 data, so we can store it directly
    // Convert base64 to buffer
    const audioBuffer = Buffer.from(audioData, 'base64');

    logger.log('[TTS Cache] Audio data info:', {
      originalBase64Length: audioData.length,
      bufferSize: audioBuffer.length
    });

    // Store in Vercel Blob with appropriate content type and cache headers
    // Use MP3 content type since ElevenLabs returns MP3 format
    const blob = await put(cacheKey, audioBuffer, {
      access: 'public',
      contentType: 'audio/mpeg', // MP3 format from ElevenLabs
      cacheControlMaxAge: CACHE_CONFIG.EXPIRY_DAYS * 24 * 60 * 60, // Convert days to seconds
    });

    logger.log('[TTS Cache] Successfully stored audio in cache:', {
      key: cacheKey,
      url: blob.url,
      size: audioBuffer.length
    });

    return blob.url;
  } catch (error) {
    logger.error('[TTS Cache] Failed to store audio in cache:', error);
    throw error;
  }
}

/**
 * Delete a cached audio file
 * @param cacheKey The cache key to delete
 * @returns Promise<boolean> indicating success
 */
export async function deleteCachedAudio(cacheKey: string): Promise<boolean> {
  try {
    logger.log('[TTS Cache] Deleting cached audio for key:', cacheKey);
    await del(cacheKey);
    logger.log('[TTS Cache] Successfully deleted cached audio:', cacheKey);
    return true;
  } catch (error) {
    logger.error('[TTS Cache] Failed to delete cached audio:', cacheKey, error);
    return false;
  }
}

/**
 * Validate if text is suitable for caching
 * @param text The text to validate
 * @returns boolean indicating if text should be cached
 */
export function shouldCacheText(text: string): boolean {
  const trimmedText = text.trim();

  // Don't cache empty or very short texts
  if (trimmedText.length < 3) {
    return false;
  }

  // Don't cache very long texts
  if (trimmedText.length > CACHE_CONFIG.MAX_TEXT_LENGTH) {
    return false;
  }

  // Don't cache texts that contain sensitive information patterns
  const sensitivePatterns = [
    /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/, // Credit card numbers
    /\b\d{3}-\d{2}-\d{4}\b/, // SSN pattern
    /password|secret|token|key/i, // Sensitive keywords
  ];

  for (const pattern of sensitivePatterns) {
    if (pattern.test(trimmedText)) {
      logger.log('[TTS Cache] Skipping cache for text with sensitive content');
      return false;
    }
  }

  return true;
}

/**
 * Get cache statistics by listing all cached files
 * @returns Promise with cache statistics
 */
export async function getCacheStats() {
  try {
    const { list } = await import('@vercel/blob');

    const { blobs } = await list({
      prefix: CACHE_CONFIG.PREFIX,
      limit: 1000
    });

    const now = new Date();
    const expiryThreshold = new Date(now.getTime() - (CACHE_CONFIG.EXPIRY_DAYS * 24 * 60 * 60 * 1000));

    let totalSize = 0;
    let expiredCount = 0;
    let expiredSize = 0;

    for (const blob of blobs) {
      totalSize += blob.size;

      const uploadedAt = new Date(blob.uploadedAt);
      if (uploadedAt < expiryThreshold) {
        expiredCount++;
        expiredSize += blob.size;
      }
    }

    return {
      totalFiles: blobs.length,
      totalSize,
      expiredFiles: expiredCount,
      expiredSize,
      cacheExpiryDays: CACHE_CONFIG.EXPIRY_DAYS
    };
  } catch (error) {
    logger.error('[TTS Cache] Failed to get cache stats:', error);
    throw error;
  }
}

/**
 * Manually trigger cache cleanup (for testing purposes)
 * @returns Promise with cleanup results
 */
export async function triggerCacheCleanup() {
  try {
    const response = await fetch('/api/cron/tts-cache-cleanup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error('Failed to trigger cache cleanup');
    }

    return await response.json();
  } catch (error) {
    logger.error('[TTS Cache] Failed to trigger cache cleanup:', error);
    throw error;
  }
}
