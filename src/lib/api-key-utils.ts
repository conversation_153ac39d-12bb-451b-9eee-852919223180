import { logger } from '@/lib/logger';

// Encryption key for API keys (in production, this should be from environment variables)
const ENCRYPTION_KEY = process.env.API_KEY_ENCRYPTION_KEY || 'default-key-for-dev-only-change-in-prod-32chars';

// Only import crypto on server-side
function getCrypto() {
  if (typeof window !== 'undefined') {
    throw new Error('Crypto operations can only be performed on the server-side');
  }
  return require('crypto');
}

/**
 * Encrypt an API key for secure storage
 * @param apiKey The plain text API key
 * @returns Encrypted API key with IV and auth tag
 */
export function encryptApiKey(apiKey: string): string {
  try {
    const crypto = getCrypto();

    // Use AES-256-CBC encryption
    const algorithm = 'aes-256-cbc';

    // Create a hash of the encryption key to ensure it's 32 bytes
    const key = crypto.createHash('sha256').update(ENCRYPTION_KEY).digest();

    // Generate a random initialization vector
    const iv = crypto.randomBytes(16);

    // Create cipher with key and iv
    const cipher = crypto.createCipheriv(algorithm, key, iv);

    // Encrypt the API key
    let encrypted = cipher.update(apiKey, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // Combine IV and encrypted data
    const result = iv.toString('hex') + ':' + encrypted;

    logger.log('[Encryption] API key encrypted successfully');
    return result;
  } catch (error) {
    logger.error('[Encryption] Error encrypting API key:', error);
    logger.error('[Encryption] Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      name: error instanceof Error ? error.name : 'Unknown',
      encryptionKey: ENCRYPTION_KEY ? 'present' : 'missing',
      algorithm: 'aes-256-cbc'
    });
    throw new Error('Failed to encrypt API key');
  }
}

/**
 * Decrypt an API key from storage
 * @param encryptedApiKey The encrypted API key string
 * @returns Decrypted plain text API key
 */
export function decryptApiKey(encryptedApiKey: string): string {
  try {
    const crypto = getCrypto();

    // Use the same AES-256-CBC algorithm for decryption
    const algorithm = 'aes-256-cbc';

    // Create a hash of the encryption key to ensure it's 32 bytes
    const key = crypto.createHash('sha256').update(ENCRYPTION_KEY).digest();

    // Split the encrypted string into components
    const parts = encryptedApiKey.split(':');
    if (parts.length !== 2) {
      throw new Error('Invalid encrypted API key format');
    }

    const iv = Buffer.from(parts[0], 'hex');
    const encrypted = parts[1];

    // Create decipher with key and iv
    const decipher = crypto.createDecipheriv(algorithm, key, iv);

    // Decrypt the API key
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    logger.log('[Decryption] API key decrypted successfully');
    return decrypted;
  } catch (error) {
    logger.error('[Decryption] Error decrypting API key:', error);
    logger.error('[Decryption] Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      name: error instanceof Error ? error.name : 'Unknown',
      encryptionKey: ENCRYPTION_KEY ? 'present' : 'missing',
      algorithm: 'aes-256-cbc'
    });
    throw new Error('Failed to decrypt API key');
  }
}

/**
 * Validate an ElevenLabs API key format
 * @param apiKey The API key to validate
 * @returns True if the format appears valid
 */
export function validateElevenLabsApiKeyFormat(apiKey: string): boolean {
  // ElevenLabs API keys typically start with 'sk-' and are around 32-64 characters
  const trimmed = apiKey.trim();
  
  // Basic format validation
  if (!trimmed || trimmed.length < 20 || trimmed.length > 100) {
    return false;
  }
  
  // Should not contain spaces or special characters except hyphens and underscores
  const validPattern = /^[a-zA-Z0-9_-]+$/;
  if (!validPattern.test(trimmed)) {
    return false;
  }
  
  return true;
}

/**
 * Mask an API key for display purposes
 * @param apiKey The API key to mask
 * @returns Masked API key showing only first 8 and last 4 characters
 */
export function maskApiKey(apiKey: string): string {
  if (!apiKey || apiKey.length < 12) {
    return '••••••••••••';
  }
  
  const start = apiKey.substring(0, 8);
  const end = apiKey.substring(apiKey.length - 4);
  const middle = '•'.repeat(Math.max(4, apiKey.length - 12));
  
  return `${start}${middle}${end}`;
}

/**
 * Test an ElevenLabs API key by making a simple API call
 * @param apiKey The API key to test
 * @returns Promise<boolean> indicating if the key is valid
 */
export async function testElevenLabsApiKey(apiKey: string): Promise<{ valid: boolean; error?: string }> {
  try {
    // Validate format first
    if (!validateElevenLabsApiKeyFormat(apiKey)) {
      return { valid: false, error: 'Invalid API key format' };
    }

    // Test the API key by making a minimal TTS request with a very short text
    // This works with scoped API keys that only have text-to-speech access
    // Using Rachel voice ID which is commonly available
    const response = await fetch('https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM', {
      method: 'POST',
      headers: {
        'xi-api-key': apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: 'Test',
        model_id: 'eleven_flash_v2_5',
        voice_settings: {
          stability: 0.5,
          similarity_boost: 0.8,
          style: 0.0,
          use_speaker_boost: true
        }
      }),
    });

    if (response.ok) {
      // Don't actually download the audio, just check if the request was accepted
      return { valid: true };
    } else if (response.status === 401) {
      return { valid: false, error: 'Invalid API key or insufficient permissions' };
    } else if (response.status === 403) {
      return { valid: false, error: 'API key does not have text-to-speech permissions' };
    } else if (response.status === 429) {
      return { valid: false, error: 'API rate limit exceeded' };
    } else if (response.status === 422) {
      // This might happen if the voice ID is not available, but the API key is valid
      return { valid: true };
    } else {
      const errorText = await response.text().catch(() => 'Unknown error');
      logger.error('API key test error:', { status: response.status, error: errorText });
      return { valid: false, error: `API error: ${response.status}` };
    }
  } catch (error) {
    logger.error('Error testing ElevenLabs API key:', error);
    return { valid: false, error: 'Failed to test API key' };
  }
}
