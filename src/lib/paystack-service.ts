"use client";

import axios from 'axios';
import { useAuth } from './auth-context';
import { useCallback } from 'react';
import { SubscriptionTier } from './usage-service';
import { logger } from '@/lib/logger';

// Mapping our application tiers to Paystack plan codes
// Using NEXT_PUBLIC_ prefixed environment variables for client-side access
const PLAN_CODES: Record<string, string> = {
  'BASIC': process.env.NEXT_PUBLIC_BASIC_PLAN_CODE || '',
  'PRO': process.env.NEXT_PUBLIC_PRO_PLAN_CODE || '',
};

// Interface for the initialize payment response
interface InitializePaymentResponse {
  authorization_url: string;
  access_code: string;
  reference: string;
}

// Interface for subscription data
export interface PaystackSubscription {
  id: number;
  subscription_code: string;
  email_token: string;
  amount: number;
  status: 'active' | 'cancelled' | 'completed' | 'non-renewing' | 'attention';
  plan: {
    name: string;
    plan_code: string;
  };
  next_payment_date: string;
}

/**
 * Initialize a subscription transaction
 * This is used to initiate the payment process for a subscription
 */
export async function initializeSubscription(
  email: string,
  tier: SubscriptionTier
): Promise<InitializePaymentResponse> {
  if (tier === 'FREE') {
    throw new Error('Cannot create subscription for FREE tier');
  }

  const planCode = PLAN_CODES[tier];
  if (!planCode) {
    throw new Error(`Invalid subscription tier: ${tier}`);
  }

  try {
    // We're using the Next.js API route to proxy the request to Paystack
    // This keeps the secret key on the server
    const response = await axios.post('/api/paystack/initialize-transaction', {
      email,
      plan: planCode,
    });

    if (response.data.status) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || 'Failed to initialize payment');
    }
  } catch (error) {
    logger.error('Error initializing subscription:', error);
    throw error;
  }
}

/**
 * Verify a transaction by reference
 * This is used to confirm if a payment was successful
 */
export async function verifyTransaction(reference: string): Promise<boolean> {
  try {
    const response = await axios.get(`/api/paystack/verify-transaction/${reference}`);
    return response.data.status && response.data.data.status === 'success';
  } catch (error) {
    logger.error('Error verifying transaction:', error);
    return false;
  }
}

/**
 * Get active subscription for a user
 */
export async function getUserSubscription(email: string): Promise<PaystackSubscription | null> {
  try {
    logger.log(`[PaystackService] Getting subscription for email: ${email}`);
    const response = await axios.get(`/api/paystack/subscription/${email}`);
    
    logger.log(`[PaystackService] Subscription API response:`, {
      status: response.data.status,
      hasData: !!response.data.data,
      dataLength: response.data.data ? response.data.data.length : 0
    });
    
    if (response.data.status && response.data.data && Array.isArray(response.data.data) && response.data.data.length > 0) {
      // Look for any subscription with status containing 'active'
      // This handles 'active', 'active (renewing)', etc.
      const allSubscriptions = response.data.data;
      logger.log(`[PaystackService] Found ${allSubscriptions.length} subscriptions:`, 
        allSubscriptions.map((sub: PaystackSubscription) => ({
          code: sub.subscription_code,
          status: sub.status,
          plan: sub.plan?.name || 'Unknown'
        }))
      );
      
      // Look for any subscription that is currently active (including non-renewing)
      // According to Paystack docs, 'non-renewing' subscriptions are still active until expiration
      let activeSubscription = allSubscriptions.find(
        (sub: PaystackSubscription) =>
          sub.status === 'active' || sub.status === 'non-renewing'
      );

      // If no exact match found, look for any status containing 'active'
      if (!activeSubscription) {
        activeSubscription = allSubscriptions.find(
          (sub: PaystackSubscription) =>
            typeof sub.status === 'string' &&
            sub.status.toLowerCase().includes('active')
        );
      }
      
      if (activeSubscription) {
        logger.log(`[PaystackService] Found active subscription:`, {
          code: activeSubscription.subscription_code,
          status: activeSubscription.status,
          plan: activeSubscription.plan?.name || 'Unknown'
        });
      } else {
        logger.log(`[PaystackService] No active subscription found among existing subscriptions`);
      }
      
      return activeSubscription || null;
    }
    logger.log(`[PaystackService] No subscriptions found for email: ${email}`);
    return null;
  } catch (error) {
    logger.error('[PaystackService] Error getting user subscription:', error);
    // Don't throw the error, just return null to indicate no active subscription
    return null;
  }
}

/**
 * Cancel a subscription
 */
export async function cancelSubscription(subscriptionCode: string, emailToken: string): Promise<boolean> {
  try {
    logger.log(`Attempting to cancel subscription: ${subscriptionCode}`);
    const response = await axios.post('/api/paystack/cancel-subscription', {
      code: subscriptionCode,
      token: emailToken
    });

    if (response.data.status) {
      logger.log(`Successfully cancelled subscription: ${subscriptionCode}`);
      return true;
    } else {
      logger.error('Subscription cancellation failed:', response.data.message);
      return false;
    }
  } catch (error: any) {
    logger.error('Error canceling subscription:', error.response?.data || error.message);
    return false;
  }
}

// Note: Paystack does not support true subscription reactivation for cancelled subscriptions.
// Users who want to resubscribe after cancellation need to create a new subscription.

/**
 * Synchronize user's subscription status between Paystack and Firebase
 * This function ensures Firebase data aligns with Paystack subscription status
 */
export async function syncUserSubscription(userId: string, userEmail: string): Promise<{
  success: boolean;
  updated: boolean;
  previousTier?: SubscriptionTier;
  newTier?: SubscriptionTier;
  error?: string;
}> {
  try {
    logger.log(`[SYNC] Starting subscription sync for user: ${userId} (${userEmail})`);

    // Validate inputs
    if (!userId || !userEmail) {
      throw new Error('Invalid user ID or email provided');
    }

    // Get current subscription status from Paystack
    const paystackSubscription = await getUserSubscription(userEmail);
    logger.log(`[SYNC] Paystack subscription status:`, {
      hasSubscription: !!paystackSubscription,
      status: paystackSubscription?.status,
      planCode: paystackSubscription?.plan?.plan_code,
      nextPaymentDate: paystackSubscription?.next_payment_date
    });

    // Determine tier from Paystack subscription
    let paystackTier: SubscriptionTier = 'FREE';
    if (paystackSubscription && (paystackSubscription.status === 'active' || paystackSubscription.status === 'non-renewing')) {
      // Map plan code to tier
      const planCode = paystackSubscription.plan?.plan_code;
      logger.log(`[SYNC] Mapping plan code to tier:`, {
        planCode,
        basicPlanCode: process.env.NEXT_PUBLIC_BASIC_PLAN_CODE,
        proPlanCode: process.env.NEXT_PUBLIC_PRO_PLAN_CODE
      });

      if (planCode === process.env.NEXT_PUBLIC_BASIC_PLAN_CODE) {
        paystackTier = 'BASIC';
      } else if (planCode === process.env.NEXT_PUBLIC_PRO_PLAN_CODE) {
        paystackTier = 'PRO';
      } else {
        logger.log(`[SYNC] Warning: Unknown plan code: ${planCode}`);
      }
    }

    logger.log(`[SYNC] Determined Paystack tier: ${paystackTier}`);

    // Get current tier from Firebase
    const { db, doc, getDoc, updateDoc, setDoc } = await import('./firebase');
    const userUsageRef = doc(db, 'userUsage', userId);
    const userUsageDoc = await getDoc(userUsageRef);

    let firebaseTier: SubscriptionTier = 'FREE';
    let firebaseData = null;
    if (userUsageDoc.exists()) {
      firebaseData = userUsageDoc.data();
      firebaseTier = firebaseData.tier || 'FREE';
    }

    logger.log(`[SYNC] Current Firebase tier: ${firebaseTier}`, {
      documentExists: userUsageDoc.exists(),
      hasData: !!firebaseData,
      lastUpdated: firebaseData?.lastUpdatedAt,
      lastSynced: firebaseData?.syncedAt
    });

    // Compare tiers and sync if needed
    if (paystackTier === firebaseTier) {
      logger.log(`[SYNC] Tiers match (${paystackTier}), no sync needed`);
      return { success: true, updated: false };
    }

    logger.log(`[SYNC] Tier mismatch detected - Firebase: ${firebaseTier}, Paystack: ${paystackTier}`);

    // Update Firebase to match Paystack (Paystack is source of truth)
    try {
      if (userUsageDoc.exists()) {
        await updateDoc(userUsageRef, {
          tier: paystackTier,
          lastUpdatedAt: new Date(),
          syncedAt: new Date(),
          syncedFromPaystack: true,
          // Clear any expiration flags if upgrading from FREE
          ...(paystackTier !== 'FREE' && {
            subscriptionExpired: false,
            expiredAt: null
          })
        });
        logger.log(`[SYNC] Updated existing Firebase document`);
      } else {
        // Create new usage record
        await setDoc(userUsageRef, {
          dailyUploads: 0,
          monthlyUploads: 0,
          lastUploadAt: null,
          lastResetAt: new Date(),
          tier: paystackTier,
          lastUpdatedAt: new Date(),
          syncedAt: new Date(),
          syncedFromPaystack: true
        });
        logger.log(`[SYNC] Created new Firebase document`);
      }

      logger.log(`[SYNC] Successfully updated Firebase tier from ${firebaseTier} to ${paystackTier}`);
    } catch (firebaseError) {
      logger.error(`[SYNC] Firebase update failed:`, firebaseError);
      throw new Error(`Failed to update Firebase: ${firebaseError instanceof Error ? firebaseError.message : 'Unknown error'}`);
    }

    return {
      success: true,
      updated: true,
      previousTier: firebaseTier,
      newTier: paystackTier
    };

  } catch (error) {
    logger.error('[SYNC] Error during subscription sync:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      userId,
      userEmail
    });

    // Categorize errors for better handling
    let errorMessage = 'Unknown error occurred during sync';
    if (error instanceof Error) {
      if (error.message.includes('Firebase')) {
        errorMessage = 'Database update failed';
      } else if (error.message.includes('Paystack') || error.message.includes('API')) {
        errorMessage = 'Payment provider communication failed';
      } else if (error.message.includes('Invalid')) {
        errorMessage = 'Invalid user data provided';
      } else {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      updated: false,
      error: errorMessage
    };
  }
}

/**
 * React hook for using Paystack services
 */
export function usePaystackService() {
  const { user } = useAuth();

  const initializeUserSubscription = useCallback(
    async (tier: SubscriptionTier) => {
      if (!user || !user.email) {
        throw new Error('User must be logged in to create subscription');
      }
      return initializeSubscription(user.email, tier);
    },
    [user]
  );

  const getUserActiveSubscription = useCallback(async () => {
    if (!user || !user.email) {
      return null;
    }
    return getUserSubscription(user.email);
  }, [user]);

  const cancelUserSubscription = useCallback(
    async (subscriptionCode: string, emailToken: string) => {
      if (!user) {
        throw new Error('User must be logged in to cancel subscription');
      }
      return cancelSubscription(subscriptionCode, emailToken);
    },
    [user]
  );

  const syncUserSubscriptionStatus = useCallback(async () => {
    if (!user || !user.email) {
      return { success: false, updated: false, error: 'User must be logged in' };
    }
    return syncUserSubscription(user.id, user.email);
  }, [user]);

  return {
    initializeSubscription: initializeUserSubscription,
    getSubscription: getUserActiveSubscription,
    cancelSubscription: cancelUserSubscription,
    syncSubscription: syncUserSubscriptionStatus,
    verifyTransaction,
  };
} 