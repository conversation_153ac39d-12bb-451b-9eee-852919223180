"use client";

import React from 'react';
import { logger } from '@/lib/logger';
import { db } from '@/lib/firebase';
import { doc, getDoc } from 'firebase/firestore';

// Exchange rate cache interface
interface ExchangeRateCache {
  rate: number;
  timestamp: number;
  source: 'api' | 'fallback' | 'firestore';
}

// Firestore exchange rate data interface
interface FirestoreExchangeRateData {
  zarToUsdRate: number;
  usdToZarRate: number;
  lastUpdated: {
    seconds: number;
    nanoseconds: number;
  };
  source: 'api' | 'fallback';
  apiResponse?: any;
  error?: string;
}

// Cache duration: 1 hour (3600000 ms)
const CACHE_DURATION = 60 * 60 * 1000;

// Fallback rate: 1 ZAR = 0.0558 USD (updated June 2025)
const FALLBACK_RATE = 0.0558;

// In-memory cache for exchange rates
let exchangeRateCache: ExchangeRateCache | null = null;

/**
 * Fetches the current exchange rate from Firestore (cached by Cloud Function)
 * Falls back to a static rate if Firestore data is unavailable or stale
 */
async function fetchExchangeRateFromFirestore(): Promise<number> {
  try {
    logger.log('[Currency Service] Fetching exchange rate from Firestore');

    const exchangeRateDoc = doc(db, 'exchangeRates', 'current');
    const docSnapshot = await getDoc(exchangeRateDoc);

    if (!docSnapshot.exists()) {
      throw new Error('Exchange rate document does not exist in Firestore');
    }

    const data = docSnapshot.data() as FirestoreExchangeRateData;

    // Validate data structure
    if (!data.zarToUsdRate || typeof data.zarToUsdRate !== 'number') {
      throw new Error('Invalid exchange rate data structure in Firestore');
    }

    // Check if data is stale (older than 25 hours - allowing for some delay)
    const lastUpdatedMs = data.lastUpdated.seconds * 1000;
    const now = Date.now();
    const ageHours = (now - lastUpdatedMs) / (1000 * 60 * 60);

    if (ageHours > 25) {
      logger.warn('[Currency Service] Exchange rate data is stale', {
        ageHours: ageHours.toFixed(2),
        lastUpdated: new Date(lastUpdatedMs).toISOString(),
      });
      // Still use the data but log a warning
    }

    logger.log('[Currency Service] Successfully fetched exchange rate from Firestore:', {
      zarToUsdRate: data.zarToUsdRate,
      source: data.source,
      ageHours: ageHours.toFixed(2),
    });

    return data.zarToUsdRate;
  } catch (error) {
    logger.error('[Currency Service] Failed to fetch exchange rate from Firestore:', error);
    throw error;
  }
}

/**
 * Gets the ZAR to USD exchange rate with caching
 * Returns cached rate if available and not expired, otherwise fetches from Firestore
 */
export async function getZarToUsdRate(): Promise<ExchangeRateCache> {
  const now = Date.now();

  // Check if we have a valid cached rate
  if (exchangeRateCache && (now - exchangeRateCache.timestamp) < CACHE_DURATION) {
    logger.log('[Currency Service] Using cached exchange rate:', exchangeRateCache);
    return exchangeRateCache;
  }

  try {
    // Try to fetch fresh rate from Firestore
    const rate = await fetchExchangeRateFromFirestore();

    exchangeRateCache = {
      rate,
      timestamp: now,
      source: 'firestore'
    };

    return exchangeRateCache;
  } catch (error) {
    logger.warn('[Currency Service] Firestore failed, using fallback rate:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      fallbackRate: FALLBACK_RATE
    });

    // Use fallback rate
    exchangeRateCache = {
      rate: FALLBACK_RATE,
      timestamp: now,
      source: 'fallback'
    };

    return exchangeRateCache;
  }
}

/**
 * Converts ZAR amount to USD using current exchange rate
 */
export async function convertZarToUsd(zarAmount: number): Promise<{
  usdAmount: number;
  rate: number;
  source: 'api' | 'fallback' | 'firestore';
  formatted: string;
}> {
  const { rate, source } = await getZarToUsdRate();
  const usdAmount = zarAmount * rate;

  // Format to 2 decimal places
  const formatted = `$${usdAmount.toFixed(2)}`;

  return {
    usdAmount,
    rate,
    source,
    formatted
  };
}

/**
 * Formats a ZAR price with USD equivalent
 * Example: "R29 (~$1.50)"
 */
export async function formatPriceWithUsd(zarAmount: number): Promise<string> {
  if (zarAmount === 0) {
    return 'R0 (~$0)';
  }

  try {
    const { formatted } = await convertZarToUsd(zarAmount);
    return `R${zarAmount} (~${formatted})`;
  } catch (error) {
    logger.error('[Currency Service] Failed to format price with USD:', error);
    // Fallback to ZAR only if conversion fails completely
    return `R${zarAmount}`;
  }
}

/**
 * React hook for currency conversion with loading state
 */
export function useCurrencyConversion() {
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const convertPrice = async (zarAmount: number): Promise<string> => {
    if (zarAmount === 0) {
      return 'R0 (~$0)';
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await formatPriceWithUsd(zarAmount);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Currency conversion failed';
      setError(errorMessage);
      logger.error('[Currency Hook] Conversion failed:', err);
      return `R${zarAmount}`; // Fallback to ZAR only
    } finally {
      setIsLoading(false);
    }
  };

  return {
    convertPrice,
    isLoading,
    error
  };
}


