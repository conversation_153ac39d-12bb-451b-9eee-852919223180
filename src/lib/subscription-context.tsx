"use client";

import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './auth-context';
import { SubscriptionTier, getUserTier } from './usage-service';
import { logger } from '@/lib/logger';

interface SubscriptionContextType {
  tier: SubscriptionTier;
  isLoading: boolean;
  isPro: boolean;
  isBasic: boolean;
  isFree: boolean;
  hasTextToSpeech: boolean;
  requiresOwnApiKey: boolean;
  subscriptionsEnabled: boolean;
  refreshSubscription: () => Promise<void>;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export function SubscriptionProvider({ children }: { children: React.ReactNode }) {
  const { user, loading: authLoading } = useAuth();
  const [tier, setTier] = useState<SubscriptionTier>('FREE');
  const [isLoading, setIsLoading] = useState(true);

  // Check if subscriptions are enabled via environment variable
  const subscriptionsEnabled = process.env.NEXT_PUBLIC_ENABLE_SUBSCRIPTIONS === 'true';

  // Cache for subscription data to avoid repeated API calls - using ref to prevent infinite loops
  const cacheRef = useRef<{
    userId?: string;
    tier?: SubscriptionTier;
    timestamp?: number;
  }>({});

  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

  const loadUserTier = useCallback(async (userId: string, forceRefresh = false) => {
    const currentCache = cacheRef.current;

    // Check cache first (unless force refresh)
    if (!forceRefresh && currentCache.userId === userId && currentCache.tier && currentCache.timestamp) {
      const isExpired = Date.now() - currentCache.timestamp > CACHE_DURATION;
      if (!isExpired) {
        logger.log('[Subscription Context] Using cached tier:', currentCache.tier);
        setTier(currentCache.tier);
        setIsLoading(false);
        return;
      }
    }

    logger.log('[Subscription Context] Loading tier for user:', userId);
    try {
      setIsLoading(true);
      const userTier = await getUserTier(userId);

      // Update cache
      cacheRef.current = {
        userId,
        tier: userTier,
        timestamp: Date.now()
      };

      setTier(userTier);
      logger.log('[Subscription Context] User tier loaded and cached:', userTier);
    } catch (error) {
      logger.error('[Subscription Context] Error loading user tier:', error);
      setTier('FREE');
    } finally {
      setIsLoading(false);
    }
  }, []); // No dependencies to prevent infinite loop

  // Load subscription data when user changes
  useEffect(() => {
    if (authLoading) {
      logger.log('[Subscription Context] Auth still loading, waiting...');
      return;
    }

    if (!user) {
      logger.log('[Subscription Context] No user found, setting tier to FREE');
      setTier('FREE');
      setIsLoading(false);
      cacheRef.current = {}; // Clear cache
      return;
    }

    // Use tier from user object if available (from AuthContext), otherwise fetch
    if (user.tier) {
      logger.log('[Subscription Context] Using tier from user object:', user.tier);
      setTier(user.tier);
      setIsLoading(false);

      // Update cache with user object data
      cacheRef.current = {
        userId: user.id,
        tier: user.tier,
        timestamp: Date.now()
      };
    } else {
      // Fallback to fetching if not available in user object
      loadUserTier(user.id);
    }
  }, [user?.id, user?.tier, authLoading]); // Only depend on specific user properties

  // Function to manually refresh subscription (for use after payment completion)
  const refreshSubscription = useCallback(async () => {
    if (user) {
      await loadUserTier(user.id, true); // Force refresh
    }
  }, [user?.id, loadUserTier]); // Use user?.id instead of user to prevent unnecessary recreations

  // Determine feature access based on tier and subscription settings
  const isPro = tier === 'PRO';
  const isBasic = tier === 'BASIC';
  const isFree = tier === 'FREE';

  // Text-to-speech access logic:
  // - TTS is now universally available for all users
  // - PRO users: use application's ElevenLabs API key
  // - FREE/BASIC users: must provide their own ElevenLabs API key
  const hasTextToSpeech = true;

  // Determine if user needs to provide their own API key
  // - PRO users: use application's ElevenLabs API key
  // - FREE/BASIC users: must provide their own ElevenLabs API key
  const requiresOwnApiKey = !isPro;

  const value: SubscriptionContextType = {
    tier,
    isLoading: isLoading || authLoading,
    isPro,
    isBasic,
    isFree,
    hasTextToSpeech,
    requiresOwnApiKey,
    subscriptionsEnabled,
    refreshSubscription,
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
}

export function useSubscription(): SubscriptionContextType {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
}
