import { initializeApp, cert, getApps } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { logger } from './logger';

// Try to get service account from JSON string first, then fall back to individual env vars
let serviceAccount: any;

if (process.env.FIREBASE_ADMIN_SERVICE_ACCOUNT_JSON) {
  logger.log('[Firebase Admin] Using service account from JSON string');
  try {
    serviceAccount = JSON.parse(process.env.FIREBASE_ADMIN_SERVICE_ACCOUNT_JSON);
  } catch (error) {
    logger.error('[Firebase Admin] Failed to parse FIREBASE_ADMIN_SERVICE_ACCOUNT_JSON:', error);
    throw new Error('Invalid FIREBASE_ADMIN_SERVICE_ACCOUNT_JSON format');
  }
} else {
  logger.log('[Firebase Admin] Using service account from individual environment variables');
  serviceAccount = {
    type: process.env.FIREBASE_ADMIN_TYPE || 'service_account',
    project_id: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    private_key_id: process.env.FIREBASE_ADMIN_PRIVATE_KEY_ID,
    private_key: process.env.FIREBASE_ADMIN_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    client_email: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
    client_id: process.env.FIREBASE_ADMIN_CLIENT_ID,
    auth_uri: process.env.FIREBASE_ADMIN_AUTH_URI || 'https://accounts.google.com/o/oauth2/auth',
    token_uri: process.env.FIREBASE_ADMIN_TOKEN_URI || 'https://oauth2.googleapis.com/token',
    auth_provider_x509_cert_url: process.env.FIREBASE_ADMIN_AUTH_PROVIDER_CERT_URL || 'https://www.googleapis.com/oauth2/v1/certs',
    client_x509_cert_url: process.env.FIREBASE_ADMIN_CLIENT_CERT_URL,
  };
}

// Initialize Firebase Admin if it hasn't been initialized yet
logger.log('[Firebase Admin] Checking Firebase Admin initialization...');
logger.log('[Firebase Admin] Current apps count:', getApps().length);

if (!getApps().length) {
  logger.log('[Firebase Admin] No apps found, initializing Firebase Admin...');

  // Log service account configuration (without sensitive data)
  logger.log('[Firebase Admin] Service account config check:', {
    hasType: !!serviceAccount.type,
    hasProjectId: !!serviceAccount.project_id,
    hasPrivateKeyId: !!serviceAccount.private_key_id,
    hasPrivateKey: !!serviceAccount.private_key,
    hasClientEmail: !!serviceAccount.client_email,
    hasClientId: !!serviceAccount.client_id,
    projectId: serviceAccount.project_id
  });

  try {
    initializeApp({
      credential: cert(serviceAccount as any),
      databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`
    });
    logger.log('[Firebase Admin] Firebase Admin initialized successfully');
  } catch (error) {
    logger.error('[Firebase Admin] Failed to initialize Firebase Admin:', error);
    throw error;
  }
} else {
  logger.log('[Firebase Admin] Firebase Admin already initialized');
}

logger.log('[Firebase Admin] Getting Auth and Firestore instances...');
export const auth = getAuth();
export const adminDb = getFirestore();
logger.log('[Firebase Admin] Auth and Firestore instances created successfully');

// Server-side function to get user tier
export async function getUserTierServer(userId: string): Promise<'FREE' | 'BASIC' | 'PRO'> {
  logger.log('[Firebase Admin] getUserTierServer called with userId:', userId);

  try {
    // Validate input
    if (!userId || typeof userId !== 'string' || userId.trim().length === 0) {
      logger.error('[Firebase Admin] Invalid userId provided:', { userId, type: typeof userId });
      return 'FREE';
    }

    logger.log('[Firebase Admin] Creating Firestore reference for userId:', userId);
    const userUsageRef = adminDb.collection('userUsage').doc(userId);

    logger.log('[Firebase Admin] Attempting to fetch document from Firestore...');
    const userDoc = await userUsageRef.get();

    logger.log('[Firebase Admin] Document fetch result:', {
      userId,
      exists: userDoc.exists,
      id: userDoc.id,
      ref: userDoc.ref.path
    });

    if (!userDoc.exists) {
      logger.log('[Firebase Admin] Document does not exist, returning FREE tier for userId:', userId);
      return 'FREE';
    }

    logger.log('[Firebase Admin] Document exists, extracting data...');
    const data = userDoc.data();

    logger.log('[Firebase Admin] Raw document data:', {
      userId,
      rawData: data,
      dataType: typeof data,
      hasData: !!data,
      keys: data ? Object.keys(data) : []
    });

    const tier = data?.tier;
    logger.log('[Firebase Admin] Tier extraction result:', {
      userId,
      extractedTier: tier,
      tierType: typeof tier,
      fallbackTier: tier || 'FREE'
    });

    const finalTier = tier || 'FREE';
    logger.log('[Firebase Admin] Final tier determination:', {
      userId,
      finalTier,
      isValidTier: ['FREE', 'BASIC', 'PRO'].includes(finalTier)
    });

    return finalTier;
  } catch (error) {
    logger.error('[Firebase Admin] Error getting user tier from server:', {
      userId,
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : error,
      errorType: typeof error
    });
    return 'FREE';
  }
}