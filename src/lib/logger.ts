/**
 * Environment-aware logging utility
 * 
 * This logger only outputs logs in development environments and completely
 * suppresses all log output in production to prevent sensitive information
 * from leaking and to improve performance.
 * 
 * Usage:
 * import { logger } from '@/lib/logger';
 * 
 * logger.log('Debug message');
 * logger.error('Error message');
 * logger.warn('Warning message');
 * logger.info('Info message');
 */

// Determine if we're in development mode
const isDevelopment = process.env.NODE_ENV === 'development';

// Create a no-op function for production
const noop = () => {};

// Logger interface that matches console methods
interface Logger {
  log: (...args: any[]) => void;
  error: (...args: any[]) => void;
  warn: (...args: any[]) => void;
  info: (...args: any[]) => void;
  debug: (...args: any[]) => void;
  trace: (...args: any[]) => void;
  group: (...args: any[]) => void;
  groupCollapsed: (...args: any[]) => void;
  groupEnd: () => void;
  table: (data: any) => void;
  time: (label?: string) => void;
  timeEnd: (label?: string) => void;
  timeLog: (label?: string, ...args: any[]) => void;
  count: (label?: string) => void;
  countReset: (label?: string) => void;
  clear: () => void;
  assert: (condition?: boolean, ...args: any[]) => void;
  dir: (obj: any, options?: any) => void;
  dirxml: (...args: any[]) => void;
}

// Create the logger object
export const logger: Logger = {
  log: isDevelopment ? console.log.bind(console) : noop,
  error: isDevelopment ? console.error.bind(console) : noop,
  warn: isDevelopment ? console.warn.bind(console) : noop,
  info: isDevelopment ? console.info.bind(console) : noop,
  debug: isDevelopment ? console.debug.bind(console) : noop,
  trace: isDevelopment ? console.trace.bind(console) : noop,
  group: isDevelopment ? console.group.bind(console) : noop,
  groupCollapsed: isDevelopment ? console.groupCollapsed.bind(console) : noop,
  groupEnd: isDevelopment ? console.groupEnd.bind(console) : noop,
  table: isDevelopment ? console.table.bind(console) : noop,
  time: isDevelopment ? console.time.bind(console) : noop,
  timeEnd: isDevelopment ? console.timeEnd.bind(console) : noop,
  timeLog: isDevelopment ? console.timeLog.bind(console) : noop,
  count: isDevelopment ? console.count.bind(console) : noop,
  countReset: isDevelopment ? console.countReset.bind(console) : noop,
  clear: isDevelopment ? console.clear.bind(console) : noop,
  assert: isDevelopment ? console.assert.bind(console) : noop,
  dir: isDevelopment ? console.dir.bind(console) : noop,
  dirxml: isDevelopment ? console.dirxml.bind(console) : noop,
};

// Export individual methods for convenience
export const { log, error, warn, info, debug, trace } = logger;

// Export a default logger for default imports
export default logger;
