/**
 * Manual test script for subscription synchronization
 * This script can be used to manually test the sync functionality
 * Run this in a development environment to verify sync works correctly
 */

import { syncUserSubscription } from './paystack-service';
import { logger } from './logger';

/**
 * Test scenarios for subscription synchronization
 */
export async function testSubscriptionSync() {
  logger.log('[TEST] Starting subscription sync tests...');
  
  // Test case 1: Invalid inputs
  logger.log('[TEST] Test 1: Invalid inputs');
  try {
    const result1 = await syncUserSubscription('', '');
    logger.log('[TEST] Result 1:', result1);
    if (!result1.success && result1.error?.includes('Invalid')) {
      logger.log('[TEST] ✅ Test 1 passed - correctly handled invalid inputs');
    } else {
      logger.log('[TEST] ❌ Test 1 failed - should have rejected invalid inputs');
    }
  } catch (error) {
    logger.log('[TEST] ❌ Test 1 failed with exception:', error);
  }
  
  // Test case 2: Valid user but no subscription (should work in dev environment)
  logger.log('[TEST] Test 2: Valid user with no subscription');
  try {
    const result2 = await syncUserSubscription('test-user-123', '<EMAIL>');
    logger.log('[TEST] Result 2:', result2);
    if (result2.success) {
      logger.log('[TEST] ✅ Test 2 passed - handled non-existent user gracefully');
    } else {
      logger.log('[TEST] ⚠️ Test 2 - expected behavior for non-existent user');
    }
  } catch (error) {
    logger.log('[TEST] Test 2 error (expected):', error);
  }
  
  logger.log('[TEST] Subscription sync tests completed');
  logger.log('[TEST] Note: For full testing, use real user data in a development environment');
}

/**
 * Test the sync hook functionality
 */
export function testSyncHookLogic() {
  logger.log('[TEST] Testing sync hook logic...');
  
  // Test rate limiting logic
  const SYNC_COOLDOWN = 10 * 60 * 1000; // 10 minutes
  const now = Date.now();
  
  // Simulate cache scenarios
  const testCases = [
    {
      name: 'No previous sync',
      cache: {},
      expected: 'should allow sync'
    },
    {
      name: 'Recent sync within cooldown',
      cache: { userId: 'test-user', lastSyncTime: now - (5 * 60 * 1000) }, // 5 minutes ago
      expected: 'should be rate limited'
    },
    {
      name: 'Old sync outside cooldown',
      cache: { userId: 'test-user', lastSyncTime: now - (15 * 60 * 1000) }, // 15 minutes ago
      expected: 'should allow sync'
    }
  ];
  
  testCases.forEach((testCase, index) => {
    const timeSinceLastSync = testCase.cache.lastSyncTime ? now - testCase.cache.lastSyncTime : Infinity;
    const shouldBeRateLimited = timeSinceLastSync < SYNC_COOLDOWN;
    
    logger.log(`[TEST] Case ${index + 1}: ${testCase.name}`);
    logger.log(`[TEST] Time since last sync: ${Math.round(timeSinceLastSync / 1000 / 60)} minutes`);
    logger.log(`[TEST] Rate limited: ${shouldBeRateLimited}`);
    logger.log(`[TEST] Expected: ${testCase.expected}`);
    
    if (testCase.expected.includes('rate limited') === shouldBeRateLimited) {
      logger.log(`[TEST] ✅ Case ${index + 1} passed`);
    } else {
      logger.log(`[TEST] ❌ Case ${index + 1} failed`);
    }
  });
  
  logger.log('[TEST] Sync hook logic tests completed');
}

/**
 * Test tier mapping logic
 */
export function testTierMapping() {
  logger.log('[TEST] Testing tier mapping logic...');
  
  const testSubscriptions = [
    {
      name: 'No subscription',
      subscription: null,
      expectedTier: 'FREE'
    },
    {
      name: 'Active BASIC subscription',
      subscription: {
        status: 'active',
        plan: { plan_code: process.env.NEXT_PUBLIC_BASIC_PLAN_CODE }
      },
      expectedTier: 'BASIC'
    },
    {
      name: 'Active PRO subscription',
      subscription: {
        status: 'active',
        plan: { plan_code: process.env.NEXT_PUBLIC_PRO_PLAN_CODE }
      },
      expectedTier: 'PRO'
    },
    {
      name: 'Non-renewing BASIC subscription',
      subscription: {
        status: 'non-renewing',
        plan: { plan_code: process.env.NEXT_PUBLIC_BASIC_PLAN_CODE }
      },
      expectedTier: 'BASIC'
    },
    {
      name: 'Cancelled subscription',
      subscription: {
        status: 'cancelled',
        plan: { plan_code: process.env.NEXT_PUBLIC_PRO_PLAN_CODE }
      },
      expectedTier: 'FREE'
    },
    {
      name: 'Unknown plan code',
      subscription: {
        status: 'active',
        plan: { plan_code: 'PLN_unknown' }
      },
      expectedTier: 'FREE'
    }
  ];
  
  testSubscriptions.forEach((testCase, index) => {
    let tier = 'FREE';
    
    if (testCase.subscription && 
        (testCase.subscription.status === 'active' || testCase.subscription.status === 'non-renewing')) {
      const planCode = testCase.subscription.plan?.plan_code;
      if (planCode === process.env.NEXT_PUBLIC_BASIC_PLAN_CODE) {
        tier = 'BASIC';
      } else if (planCode === process.env.NEXT_PUBLIC_PRO_PLAN_CODE) {
        tier = 'PRO';
      }
    }
    
    logger.log(`[TEST] Case ${index + 1}: ${testCase.name}`);
    logger.log(`[TEST] Mapped tier: ${tier}`);
    logger.log(`[TEST] Expected tier: ${testCase.expectedTier}`);
    
    if (tier === testCase.expectedTier) {
      logger.log(`[TEST] ✅ Case ${index + 1} passed`);
    } else {
      logger.log(`[TEST] ❌ Case ${index + 1} failed`);
    }
  });
  
  logger.log('[TEST] Tier mapping tests completed');
}

/**
 * Run all tests
 */
export async function runAllSyncTests() {
  logger.log('[TEST] ========================================');
  logger.log('[TEST] Running all subscription sync tests...');
  logger.log('[TEST] ========================================');
  
  testTierMapping();
  testSyncHookLogic();
  await testSubscriptionSync();
  
  logger.log('[TEST] ========================================');
  logger.log('[TEST] All tests completed');
  logger.log('[TEST] ========================================');
}

// Export for console testing
if (typeof window !== 'undefined') {
  (window as any).testSubscriptionSync = {
    runAllTests: runAllSyncTests,
    testTierMapping,
    testSyncHookLogic,
    testSubscriptionSync
  };
}
