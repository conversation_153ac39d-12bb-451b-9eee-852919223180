/**
 * <PERSON>ie Consent Management Utilities
 * Handles user consent preferences for GDPR compliance
 */

export interface CookieConsentState {
  essential: boolean;    // Always true (required for app functionality)
  analytics: boolean;    // User choice for Google Analytics
  functional: boolean;   // User choice for functional cookies (sidebar state, etc.)
  hasConsented: boolean; // Whether user has made a choice
  consentDate: string;   // When consent was given
}

const CONSENT_STORAGE_KEY = 'cookie_consent';
const CONSENT_VERSION = '1.0'; // Increment when consent requirements change

/**
 * Default consent state - analytics enabled by default as requested
 */
export const DEFAULT_CONSENT: CookieConsentState = {
  essential: true,
  analytics: true,  // Enabled by default per user preference
  functional: true,
  hasConsented: false,
  consentDate: '',
};

/**
 * Get current consent state from localStorage
 */
export function getCookieConsent(): CookieConsentState {
  if (typeof window === 'undefined') {
    return DEFAULT_CONSENT;
  }

  try {
    const stored = localStorage.getItem(CONSENT_STORAGE_KEY);
    if (!stored) {
      return DEFAULT_CONSENT;
    }

    const parsed = JSON.parse(stored);
    
    // Check if consent version matches (for future updates)
    if (parsed.version !== CONSENT_VERSION) {
      return DEFAULT_CONSENT;
    }

    return {
      essential: true, // Always true
      analytics: parsed.analytics ?? DEFAULT_CONSENT.analytics,
      functional: parsed.functional ?? DEFAULT_CONSENT.functional,
      hasConsented: parsed.hasConsented ?? false,
      consentDate: parsed.consentDate ?? '',
    };
  } catch (error) {
    console.error('Error reading cookie consent:', error);
    return DEFAULT_CONSENT;
  }
}

/**
 * Save consent state to localStorage
 */
export function setCookieConsent(consent: Partial<CookieConsentState>): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    const currentConsent = getCookieConsent();
    const newConsent: CookieConsentState = {
      ...currentConsent,
      ...consent,
      essential: true, // Always true
      hasConsented: true,
      consentDate: new Date().toISOString(),
    };

    const toStore = {
      ...newConsent,
      version: CONSENT_VERSION,
    };

    localStorage.setItem(CONSENT_STORAGE_KEY, JSON.stringify(toStore));

    // Trigger custom event for components to react to consent changes
    window.dispatchEvent(new CustomEvent('cookieConsentChanged', {
      detail: newConsent
    }));
  } catch (error) {
    console.error('Error saving cookie consent:', error);
  }
}

/**
 * Accept all cookies (default behavior)
 */
export function acceptAllCookies(): void {
  setCookieConsent({
    analytics: true,
    functional: true,
  });
}

/**
 * Decline non-essential cookies
 */
export function declineNonEssentialCookies(): void {
  setCookieConsent({
    analytics: false,
    functional: false,
  });
}

/**
 * Check if user has made a consent choice
 */
export function hasUserConsented(): boolean {
  return getCookieConsent().hasConsented;
}

/**
 * Check if analytics are enabled
 */
export function isAnalyticsEnabled(): boolean {
  return getCookieConsent().analytics;
}

/**
 * Check if functional cookies are enabled
 */
export function isFunctionalEnabled(): boolean {
  return getCookieConsent().functional;
}

/**
 * Reset consent (for testing or consent withdrawal)
 */
export function resetConsent(): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    localStorage.removeItem(CONSENT_STORAGE_KEY);
    window.dispatchEvent(new CustomEvent('cookieConsentChanged', {
      detail: DEFAULT_CONSENT
    }));
  } catch (error) {
    console.error('Error resetting consent:', error);
  }
}

/**
 * Hook for components to listen to consent changes
 */
export function useCookieConsentListener(callback: (consent: CookieConsentState) => void): (() => void) | void {
  if (typeof window === 'undefined') {
    return;
  }

  const handleConsentChange = (event: CustomEvent<CookieConsentState>) => {
    callback(event.detail);
  };

  window.addEventListener('cookieConsentChanged', handleConsentChange as EventListener);

  // Cleanup function
  return () => {
    window.removeEventListener('cookieConsentChanged', handleConsentChange as EventListener);
  };
}
