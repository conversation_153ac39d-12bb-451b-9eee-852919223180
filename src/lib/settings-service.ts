import { db, doc, getDoc, setDoc, updateDoc } from './firebase';
import type { UserSettings } from '@/types';
import { DEFAULT_TTS_VOICES, isValidTTSVoice } from './tts-voices';
import { logger } from './logger';

const DEFAULT_SETTINGS: UserSettings = {
  answerDetail: 50,
  cardRange: {
    min: 30,
    max: 50
  },
  ttsVoices: DEFAULT_TTS_VOICES
};

function validateSettings(settings: Partial<UserSettings>): { valid: boolean; error?: string } {
  // Validate answerDetail if provided
  if ('answerDetail' in settings) {
    const detail = settings.answerDetail;
    if (typeof detail !== 'number' || detail < 0 || detail > 100) {
      return { valid: false, error: 'Answer detail must be between 0 and 100' };
    }
  }

  // Validate cardRange if provided
  if ('cardRange' in settings) {
    const range = settings.cardRange;
    if (!range || typeof range !== 'object') {
      return { valid: false, error: 'Invalid card range format' };
    }

    if ('min' in range) {
      if (typeof range.min !== 'number' || range.min < 3 || range.min > 100) {
        return { valid: false, error: 'Minimum cards must be between 3 and 100' };
      }
    }

    if ('max' in range) {
      if (typeof range.max !== 'number' || range.max < 3 || range.max > 100) {
        return { valid: false, error: 'Maximum cards must be between 3 and 100' };
      }
    }

    // If both min and max are provided, ensure min <= max
    if ('min' in range && 'max' in range && range.min > range.max) {
      return { valid: false, error: 'Minimum cards cannot be greater than maximum cards' };
    }
  }

  // Validate ttsVoices if provided
  if ('ttsVoices' in settings) {
    const voices = settings.ttsVoices;
    if (voices && typeof voices === 'object') {
      if ('questionVoice' in voices && voices.questionVoice && !isValidTTSVoice(voices.questionVoice)) {
        return { valid: false, error: 'Invalid question voice selection' };
      }
      if ('answerVoice' in voices && voices.answerVoice && !isValidTTSVoice(voices.answerVoice)) {
        return { valid: false, error: 'Invalid answer voice selection' };
      }
    }
  }

  // Note: ElevenLabs API key validation is now handled server-side
  // for security reasons (encryption/decryption happens on server)

  return { valid: true };
}

export function useSettingsService() {
  const getUserSettings = async (userId: string): Promise<UserSettings> => {
    try {
      const userDocRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userDocRef);

      if (!userDoc.exists()) {
        // If user document doesn't exist, create it with default settings
        try {
          await setDoc(userDocRef, {
            settings: DEFAULT_SETTINGS,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          });
        } catch (error) {
          logger.error('Error creating user document:', error);
          // Even if creation fails, return default settings
          return DEFAULT_SETTINGS;
        }
        return DEFAULT_SETTINGS;
      }

      const data = userDoc.data();
      // Validate stored settings, return defaults if invalid
      const settings = data.settings || DEFAULT_SETTINGS;

      // Note: API key decryption is now handled server-side via /api/settings
      // Client-side getUserSettings only returns non-sensitive data
      // For API key access, use the server-side API route

      const validation = validateSettings(settings);
      return validation.valid ? settings : DEFAULT_SETTINGS;
    } catch (error) {
      logger.error('Error fetching user settings:', error);
      return DEFAULT_SETTINGS;
    }
  };

  const updateUserSettings = async (userId: string, settings: Partial<UserSettings>): Promise<{ success: boolean; error?: string }> => {
    try {
      // Validate settings first (client-side validation for immediate feedback)
      const validation = validateSettings(settings);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      logger.log('[Settings Service] Updating settings via API for user:', userId);
      logger.log('[Settings Service] Settings to update:', {
        ...settings,
        elevenLabsApiKey: settings.elevenLabsApiKey ? '[REDACTED]' : settings.elevenLabsApiKey
      });
      logger.log('[Settings Service] API key details:', {
        hasApiKey: 'elevenLabsApiKey' in settings,
        apiKeyValue: settings.elevenLabsApiKey,
        apiKeyType: typeof settings.elevenLabsApiKey,
        apiKeyLength: settings.elevenLabsApiKey ? settings.elevenLabsApiKey.length : 0,
        isEmpty: settings.elevenLabsApiKey === ''
      });

      // Send to server-side API for secure processing
      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          settings
        }),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        logger.error('Settings API error:', result.error);
        return { success: false, error: result.error || 'Failed to update settings' };
      }

      logger.log('Settings updated successfully via API');
      return { success: true };

    } catch (error) {
      logger.error('Error updating settings:', error);
      return { success: false, error: 'Failed to update settings' };
    }
  };

  return {
    getUserSettings,
    updateUserSettings,
    DEFAULT_SETTINGS
  };
}