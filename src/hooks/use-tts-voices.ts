"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth-context';
import { useSettingsService } from '@/lib/settings-service';
import { logger } from '@/lib/logger';
import { DEFAULT_TTS_VOICES, getSafeVoiceName, type TTSVoiceName } from '@/lib/tts-voices';

interface TTSVoicesState {
  questionVoice: TTSVoiceName;
  answerVoice: TTSVoiceName;
  isLoading: boolean;
}

export function useTTSVoices(): TTSVoicesState {
  const { user, loading: authLoading } = useAuth();
  const { getUserSettings } = useSettingsService();
  const [voices, setVoices] = useState({
    questionVoice: DEFAULT_TTS_VOICES.questionVoice,
    answerVoice: DEFAULT_TTS_VOICES.answerVoice
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadUserVoices = async () => {
      if (authLoading) {
        return;
      }

      if (!user) {
        // Use default voices for non-authenticated users
        setVoices({
          questionVoice: DEFAULT_TTS_VOICES.questionVoice,
          answerVoice: DEFAULT_TTS_VOICES.answerVoice
        });
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const settings = await getUserSettings(user.id);

        // Use saved voices or fall back to defaults
        setVoices({
          questionVoice: getSafeVoiceName(
            settings.ttsVoices?.questionVoice,
            DEFAULT_TTS_VOICES.questionVoice
          ),
          answerVoice: getSafeVoiceName(
            settings.ttsVoices?.answerVoice,
            DEFAULT_TTS_VOICES.answerVoice
          )
        });
      } catch (error) {
        logger.error('Error loading TTS voices:', error);
        // Fall back to defaults on error
        setVoices({
          questionVoice: DEFAULT_TTS_VOICES.questionVoice,
          answerVoice: DEFAULT_TTS_VOICES.answerVoice
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadUserVoices();
  }, [user, authLoading, getUserSettings]);

  return {
    questionVoice: voices.questionVoice,
    answerVoice: voices.answerVoice,
    isLoading: isLoading || authLoading,
  };
}
