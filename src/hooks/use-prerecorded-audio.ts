"use client";

import { useState, useCallback, useRef } from 'react';
import { logger } from '@/lib/logger';
import { fetchPrerecordedAudio, type TTSVoiceName } from '@/lib/tts-voices';

interface AudioState {
  isLoading: boolean;
  isPlaying: boolean;
  error: string | null;
}

export function usePrerecordedAudio() {
  const [audioState, setAudioState] = useState<AudioState>({
    isLoading: false,
    isPlaying: false,
    error: null,
  });

  const audioRef = useRef<HTMLAudioElement | null>(null);

  const stopAudio = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
    setAudioState(prev => ({ ...prev, isPlaying: false }));
  }, []);

  const playPrerecordedAudio = useCallback(async (voiceName: TTSVoiceName, type: 'question' | 'answer') => {
    try {
      logger.log('[Prerecorded Audio] Starting playback for voice:', voiceName, 'type:', type);

      // Stop any currently playing audio
      stopAudio();

      // Reset error state and set loading
      setAudioState({
        isLoading: true,
        isPlaying: false,
        error: null,
      });

      // Fetch the pre-recorded audio URL
      const audioUrl = await fetchPrerecordedAudio(voiceName, type);
      logger.log('[Prerecorded Audio] Got audio URL:', audioUrl);

      // Create and configure audio element
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      // Set up event listeners
      audio.addEventListener('loadstart', () => {
        logger.log('[Prerecorded Audio] Loading started');
      });

      audio.addEventListener('canplay', () => {
        logger.log('[Prerecorded Audio] Can play');
        setAudioState({
          isLoading: false,
          isPlaying: true,
          error: null,
        });
      });

      audio.addEventListener('ended', () => {
        logger.log('[Prerecorded Audio] Playback ended');
        setAudioState(prev => ({ ...prev, isPlaying: false }));
      });

      audio.addEventListener('error', (e) => {
        logger.error('[Prerecorded Audio] Audio error:', e);
        setAudioState({
          isLoading: false,
          isPlaying: false,
          error: 'Failed to load audio file',
        });
      });

      // Start playing
      await audio.play();

    } catch (error) {
      logger.error('[Prerecorded Audio] Error playing audio:', error);
      setAudioState({
        isLoading: false,
        isPlaying: false,
        error: error instanceof Error ? error.message : 'Failed to play audio',
      });
    }
  }, [stopAudio]);

  return {
    audioState,
    playPrerecordedAudio,
    stopAudio,
  };
}
