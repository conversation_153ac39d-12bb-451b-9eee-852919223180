import { useState, useRef, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/lib/auth-context';
import { logger } from '@/lib/logger';

interface AudioState {
  isLoading: boolean;
  isPlaying: boolean;
  error: string | null;
}

interface UseAudioReturn {
  audioState: AudioState;
  playText: (text: string, voiceName?: string) => Promise<void>;
  playAudioUrl: (audioUrl: string) => Promise<void>;
  stopAudio: () => void;
}

export function useAudio(): UseAudioReturn {
  const [audioState, setAudioState] = useState<AudioState>({
    isLoading: false,
    isPlaying: false,
    error: null,
  });

  const audioRef = useRef<HTMLAudioElement | null>(null);
  const { toast } = useToast();
  const { user } = useAuth();

  const stopAudio = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      audioRef.current = null;
    }
    setAudioState(prev => ({ ...prev, isPlaying: false }));
  }, []);

  const playText = useCallback(async (text: string, voiceName: string = 'Rachel') => {
    try {
      logger.log('[Audio Hook] Starting TTS request:', {
        textLength: text?.length,
        voiceName,
        userId: user?.id,
        hasUser: !!user
      });

      // Stop any currently playing audio
      stopAudio();

      // Reset error state and set loading
      setAudioState({
        isLoading: true,
        isPlaying: false,
        error: null,
      });

      // Validate input
      if (!text || text.trim().length === 0) {
        throw new Error('Text is required');
      }

      // Call TTS API
      const requestBody = {
        text: text.trim(),
        voiceName,
        userId: user?.id, // Include user ID for subscription validation
      };

      logger.log('[Audio Hook] Making API request with body:', requestBody);

      const response = await fetch('/api/text-to-speech', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      logger.log('[Audio Hook] API response status:', response.status);

      const data = await response.json();
      logger.log('[Audio Hook] API response data:', {
        success: data.success,
        hasAudioData: !!data.audioData,
        hasAudioUrl: !!data.audioUrl,
        cached: data.cached,
        error: data.error
      });

      // Log cache status clearly
      if (data.cached) {
        logger.log('🎯 [Audio Hook] ✅ USING CACHED AUDIO - No ElevenLabs API call made!');
      } else {
        logger.log('🤖 [Audio Hook] ⚡ USING FRESH AUDIO - Generated via ElevenLabs API');
      }

      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to generate speech');
      }

      let audioUrl: string;
      let isBlobUrl = false;

      // Helper function to create blob URL from base64 MP3 data
      const createBlobFromBase64 = (base64Data: string): string => {
        logger.log('[Audio Hook] Processing base64 MP3 audio data from ElevenLabs');

        // Convert base64 to audio blob
        // ElevenLabs returns MP3 data, so we need to handle it as MP3, not convert to WAV
        const audioBytes = atob(base64Data);
        const audioArray = new Uint8Array(audioBytes.length);
        for (let i = 0; i < audioBytes.length; i++) {
          audioArray[i] = audioBytes.charCodeAt(i);
        }

        logger.log('[Audio Hook] MP3 data info:', {
          originalBase64Length: base64Data.length,
          audioArrayLength: audioArray.length,
          firstFewBytes: Array.from(audioArray.slice(0, 10)).map(b => b.toString(16)).join(' ')
        });

        // Create MP3 blob directly - no need to add WAV headers since ElevenLabs returns MP3
        const audioBlob = new Blob([audioArray], { type: 'audio/mpeg' });
        const blobUrl = URL.createObjectURL(audioBlob);

        logger.log('[Audio Hook] Created MP3 blob URL successfully');
        return blobUrl;
      };

      // For cached audio, use the cached URL directly (MP3 format from Vercel Blob)
      if (data.audioUrl && data.cached) {
        logger.log('🔗 [Audio Hook] Using cached audio URL (MP3 format from Vercel Blob)');
        logger.log('📁 [Audio Hook] Cache URL:', data.audioUrl);

        // Use the cached URL directly - it's a proper MP3 file
        audioUrl = data.audioUrl;
        isBlobUrl = false; // This is a direct URL, not a blob URL

      } else if (data.audioData) {
        // Use base64 MP3 data directly (either no cache or new generation)
        logger.log('🎵 [Audio Hook] Converting base64 MP3 data to blob URL');
        audioUrl = createBlobFromBase64(data.audioData);
        logger.log('🔗 [Audio Hook] Created blob URL:', audioUrl.substring(0, 50) + '...');
        isBlobUrl = true;
      } else {
        throw new Error('No audio data or URL received');
      }

      // Create and configure audio element with fallback mechanism
      const audio = new Audio();
      audioRef.current = audio;

      // Set up event listeners
      audio.addEventListener('loadstart', () => {
        setAudioState(prev => ({ ...prev, isLoading: true }));
      });

      audio.addEventListener('canplay', () => {
        setAudioState(prev => ({ ...prev, isLoading: false, isPlaying: true }));
      });

      audio.addEventListener('ended', () => {
        setAudioState(prev => ({ ...prev, isPlaying: false }));
        // Only revoke blob URLs, not cached URLs
        if (isBlobUrl) {
          URL.revokeObjectURL(audioUrl);
        }
        audioRef.current = null;
      });

      // Simplified error handler - cache should work properly now
      audio.addEventListener('error', (e) => {
        logger.error('❌ [Audio Hook] Audio playback error:', e);

        // Log additional info for cached URLs to help debug
        if (data.cached && data.audioUrl && !isBlobUrl) {
          logger.error('❌ [Audio Hook] Cached URL failed to load:', data.audioUrl);
          logger.error('❌ [Audio Hook] This should not happen with properly formatted MP3 files');
        }

        setAudioState(prev => ({
          ...prev,
          isLoading: false,
          isPlaying: false,
          error: 'Failed to play audio'
        }));

        // Clean up blob URL
        if (isBlobUrl) {
          URL.revokeObjectURL(audioUrl);
        }
        audioRef.current = null;

        toast({
          variant: "destructive",
          title: "Playback Error",
          description: "Failed to play the generated audio.",
        });
      });

      // Set the audio source and start playback
      audio.src = audioUrl;
      await audio.play();

    } catch (error) {
      logger.error('❌ [Audio Hook] TTS Error:', error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      setAudioState({
        isLoading: false,
        isPlaying: false,
        error: errorMessage,
      });

      toast({
        variant: "destructive",
        title: "Speech Generation Failed",
        description: errorMessage,
      });
    }
  }, [stopAudio, toast, user]);

  const playAudioUrl = useCallback(async (audioUrl: string) => {
    try {
      logger.log('[Audio Hook] Playing audio from URL:', audioUrl);

      // Stop any currently playing audio
      stopAudio();

      // Reset error state and set loading
      setAudioState({
        isLoading: true,
        isPlaying: false,
        error: null,
      });

      // Validate URL
      if (!audioUrl || typeof audioUrl !== 'string') {
        throw new Error('Audio URL is required');
      }

      // Create and configure audio element
      const audio = new Audio();
      audioRef.current = audio;

      // Set up event listeners
      audio.onloadstart = () => {
        logger.log('[Audio Hook] Audio loading started');
      };

      audio.oncanplay = () => {
        logger.log('[Audio Hook] Audio can start playing');
        setAudioState({
          isLoading: false,
          isPlaying: true,
          error: null,
        });
      };

      audio.onended = () => {
        logger.log('[Audio Hook] Audio playback ended');
        setAudioState({
          isLoading: false,
          isPlaying: false,
          error: null,
        });
        audioRef.current = null;
      };

      audio.onerror = (e) => {
        logger.error('[Audio Hook] Audio playback error:', e);
        const errorMessage = 'Failed to play audio preview';
        setAudioState({
          isLoading: false,
          isPlaying: false,
          error: errorMessage,
        });
        toast({
          variant: "destructive",
          title: "Audio Playback Failed",
          description: errorMessage,
        });
      };

      // Set the audio source and start playback
      audio.src = audioUrl;
      await audio.play();

    } catch (error) {
      logger.error('❌ [Audio Hook] Audio URL Error:', error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      setAudioState({
        isLoading: false,
        isPlaying: false,
        error: errorMessage,
      });

      toast({
        variant: "destructive",
        title: "Audio Playback Failed",
        description: errorMessage,
      });
    }
  }, [stopAudio, toast]);

  return {
    audioState,
    playText,
    playAudioUrl,
    stopAudio,
  };
}
