"use client";

import { useCallback, useRef } from 'react';
import { useAuth } from '@/lib/auth-context';
import { usePaystackService } from '@/lib/paystack-service';
import { useSubscription } from '@/lib/subscription-context';
import { logger } from '@/lib/logger';

interface SyncCache {
  userId?: string;
  lastSyncTime?: number;
  isRunning?: boolean;
}

interface SyncResult {
  success: boolean;
  updated: boolean;
  previousTier?: string;
  newTier?: string;
  error?: string;
  skipped?: boolean;
  reason?: string;
}

/**
 * Hook for managing subscription synchronization with caching and rate limiting
 */
export function useSubscriptionSync() {
  const { user } = useAuth();
  const { syncSubscription } = usePaystackService();
  const { refreshSubscription } = useSubscription();
  
  // Cache to prevent excessive API calls
  const cacheRef = useRef<SyncCache>({});
  
  // Rate limiting: Only sync once every 10 minutes per user
  const SYNC_COOLDOWN = 10 * 60 * 1000; // 10 minutes

  // Additional cache for failed attempts to prevent spam
  const failureCacheRef = useRef<{
    userId?: string;
    failureCount?: number;
    lastFailureTime?: number;
  }>({});
  
  const syncUserSubscription = useCallback(async (options?: {
    force?: boolean;
    source?: string;
  }): Promise<SyncResult> => {
    const { force = false, source = 'manual' } = options || {};
    
    // Check if user is authenticated
    if (!user || !user.email) {
      logger.log(`[SYNC] Skipping sync - user not authenticated`);
      return { 
        success: false, 
        updated: false, 
        skipped: true, 
        reason: 'User not authenticated' 
      };
    }
    
    const now = Date.now();
    const cache = cacheRef.current;
    
    // Check if sync is already running
    if (cache.isRunning) {
      logger.log(`[SYNC] Skipping sync - already running for user: ${user.id}`);
      return { 
        success: false, 
        updated: false, 
        skipped: true, 
        reason: 'Sync already in progress' 
      };
    }
    
    // Check rate limiting (unless forced)
    if (!force && cache.userId === user.id && cache.lastSyncTime) {
      const timeSinceLastSync = now - cache.lastSyncTime;
      if (timeSinceLastSync < SYNC_COOLDOWN) {
        const remainingTime = Math.ceil((SYNC_COOLDOWN - timeSinceLastSync) / 1000 / 60);
        logger.log(`[SYNC] Skipping sync - rate limited. ${remainingTime} minutes remaining`);
        return {
          success: false,
          updated: false,
          skipped: true,
          reason: `Rate limited - ${remainingTime} minutes remaining`
        };
      }
    }

    // Check failure rate limiting (exponential backoff for failed attempts)
    const failureCache = failureCacheRef.current;
    if (!force && failureCache.userId === user.id && failureCache.failureCount && failureCache.failureCount >= 3) {
      const backoffTime = Math.min(SYNC_COOLDOWN * Math.pow(2, failureCache.failureCount - 3), 60 * 60 * 1000); // Max 1 hour
      const timeSinceLastFailure = now - (failureCache.lastFailureTime || 0);
      if (timeSinceLastFailure < backoffTime) {
        const remainingTime = Math.ceil((backoffTime - timeSinceLastFailure) / 1000 / 60);
        logger.log(`[SYNC] Skipping sync - failure backoff. ${remainingTime} minutes remaining`);
        return {
          success: false,
          updated: false,
          skipped: true,
          reason: `Failure backoff - ${remainingTime} minutes remaining`
        };
      }
    }
    
    // Mark sync as running
    cache.isRunning = true;
    
    try {
      logger.log(`[SYNC] Starting subscription sync from ${source} for user: ${user.id}`);
      
      // Perform the actual sync
      const result = await syncSubscription();
      
      // Update cache
      cache.userId = user.id;
      cache.lastSyncTime = now;

      if (result.success) {
        // Clear failure cache on success
        if (failureCache.userId === user.id) {
          failureCacheRef.current = {};
        }

        if (result.updated) {
          logger.log(`[SYNC] Subscription updated successfully:`, {
            from: result.previousTier,
            to: result.newTier,
            source
          });

          // Refresh subscription context to reflect changes
          await refreshSubscription();
        }
      } else {
        // Update failure cache
        failureCache.userId = user.id;
        failureCache.failureCount = (failureCache.failureCount || 0) + 1;
        failureCache.lastFailureTime = now;
        logger.log(`[SYNC] Sync failed, failure count: ${failureCache.failureCount}`);
      }

      return {
        ...result,
        skipped: false
      };
      
    } catch (error) {
      logger.error(`[SYNC] Error during subscription sync:`, error);
      return {
        success: false,
        updated: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        skipped: false
      };
    } finally {
      // Clear running flag
      cache.isRunning = false;
    }
  }, [user, syncSubscription, refreshSubscription]);
  
  // Force sync (bypasses rate limiting)
  const forceSyncUserSubscription = useCallback(async (source = 'force'): Promise<SyncResult> => {
    return syncUserSubscription({ force: true, source });
  }, [syncUserSubscription]);
  
  // Get sync status
  const getSyncStatus = useCallback(() => {
    const cache = cacheRef.current;
    const now = Date.now();
    
    if (!user) {
      return { canSync: false, reason: 'User not authenticated' };
    }
    
    if (cache.isRunning) {
      return { canSync: false, reason: 'Sync in progress' };
    }
    
    if (cache.userId === user.id && cache.lastSyncTime) {
      const timeSinceLastSync = now - cache.lastSyncTime;
      if (timeSinceLastSync < SYNC_COOLDOWN) {
        const remainingTime = Math.ceil((SYNC_COOLDOWN - timeSinceLastSync) / 1000 / 60);
        return { 
          canSync: false, 
          reason: `Rate limited - ${remainingTime} minutes remaining`,
          lastSyncTime: cache.lastSyncTime
        };
      }
    }
    
    return { 
      canSync: true, 
      lastSyncTime: cache.lastSyncTime 
    };
  }, [user]);
  
  // Clear cache (useful for testing or when user changes)
  const clearSyncCache = useCallback(() => {
    cacheRef.current = {};
    failureCacheRef.current = {};
    logger.log(`[SYNC] All caches cleared`);
  }, []);
  
  return {
    syncUserSubscription,
    forceSyncUserSubscription,
    getSyncStatus,
    clearSyncCache
  };
}
