"use client";

import type { ChangeEvent, FormEvent } from 'react';
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { UploadCloud, AlertCircle, Loader2, FileText, Smartphone, Info, Trash2 } from 'lucide-react';
import type { FlashcardData } from '@/types';
import { generateFlashcardsAction } from '@/app/actions';
import { useToast } from '@/hooks/use-toast';
import { ScrollArea } from "@/components/ui/scroll-area";
import { useAuth } from '@/lib/auth-context';
import { useUsageService } from '@/lib/usage-service';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useSettingsService } from '@/lib/settings-service';
import { upload } from '@vercel/blob/client';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '@/lib/logger';

interface FileUploadFormProps {
  onFlashcardsGenerated: (flashcards: FlashcardData[], title?: string) => void;
  setIsLoading: (isLoading: boolean) => void;
}

export function FileUploadForm({ onFlashcardsGenerated, setIsLoading: setAppIsLoading }: FileUploadFormProps) {
  const [files, setFiles] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [isCheckingLimits, setIsCheckingLimits] = useState(false);
  const [isDraggingOver, setIsDraggingOver] = useState(false);
  const [usageInfo, setUsageInfo] = useState<{
    dailyUploads: number;
    monthlyUploads: number;
    dailyLimit: number;
    monthlyLimit: number;
    tier: string;
  } | null>(null);
  
  const { toast } = useToast();
  const { user, loading } = useAuth();
  const { checkLimits, trackUserUpload, LIMITS } = useUsageService();
  const router = useRouter();
  const { getUserSettings, DEFAULT_SETTINGS } = useSettingsService();

  // Create a constant for allowed file types and their MIME types
  const ALLOWED_FILE_TYPES = {
    // Documents
    'application/pdf': '.pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
    'application/msword': '.doc',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx',
    'application/vnd.ms-powerpoint': '.ppt',
    'text/plain': '.txt',
    'text/markdown': '.md',
  };

  // Convert MIME type object to accept string for input element
  const ACCEPT_STRING = Object.values(ALLOWED_FILE_TYPES).join(',');

  // Helper to check if a file type is allowed
  const isAllowedFileType = (fileType: string): boolean => {
    return Object.keys(ALLOWED_FILE_TYPES).includes(fileType);
  };

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera || '';
      const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i;
      setIsMobile(mobileRegex.test(userAgent.toLowerCase()));
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Check user limits when user state changes
  useEffect(() => {
    const checkUserLimits = async () => {
      if (!user || loading) return;

      setIsCheckingLimits(true);
      try {
        const limits = await checkLimits();
        if (limits.currentUsage) {
          const tier = limits.currentUsage.tier || 'FREE';
          const tierLimits = LIMITS[tier as keyof typeof LIMITS];
          
          setUsageInfo({
            dailyUploads: limits.currentUsage.dailyUploads,
            monthlyUploads: limits.currentUsage.monthlyUploads,
            dailyLimit: tierLimits.DAILY_UPLOADS,
            monthlyLimit: tierLimits.MONTHLY_UPLOADS,
            tier
          });
        }
      } catch (error) {
        logger.error('Error checking limits:', error);
      } finally {
        setIsCheckingLimits(false);
      }
    };

    checkUserLimits();
  }, [user, loading, checkLimits, LIMITS]);

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setFiles(Array.from(event.target.files));
      setError(null); // Clear previous errors
    } else {
      setFiles([]);
    }
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    
    // Check if user is logged in
    if (!user) {
      toast({
        variant: "destructive",
        title: "Authentication Required",
        description: "Please sign in to upload files and generate flashcards.",
      });
      router.push('/auth/signin');
      return;
    }
    
    // Check rate limits
    try {
      const limits = await checkLimits();
      if (!limits.canUpload) {
        setError(limits.reason || 'Rate limit exceeded');
        toast({
          variant: "destructive",
          title: "Rate Limit Exceeded",
          description: limits.reason || 'You have reached your usage limit',
        });
        return;
      }
    } catch (error) {
      logger.error('Error checking limits:', error);
    }
    
    if (!files || files.length === 0) {
      setError('Please select at least one file to upload.');
      return;
    }

    // Validate files
    for (const file of files) {
      if (!isAllowedFileType(file.type)) {
        const errorMsg = `Invalid file type: ${file.name}. Only PDF, Word, PowerPoint, TXT, and MD files are allowed.`;
        setError(errorMsg);
        toast({ variant: "destructive", title: "File Type Error", description: errorMsg });
        return;
      }
      if (file.size > 15 * 1024 * 1024) { // 15MB limit per file
        const errorMsg = `File too large: ${file.name}. Maximum 15MB per file.`;
        setError(errorMsg);
        toast({ variant: "destructive", title: "File Size Error", description: errorMsg });
        return;
      }
    }

    setIsSubmitting(true);
    setAppIsLoading(true);
    setError(null);

    try {
      // Get user settings
      const settings = await getUserSettings(user.id);
      
      // Process each file
      for (const file of files) {
        // Get file extension from mime type
        const fileExtension = ALLOWED_FILE_TYPES[file.type as keyof typeof ALLOWED_FILE_TYPES] || '.bin';
        
        // Generate a unique filename with the correct extension
        const uniqueName = `uploads/${file.name.replace(/\.[^/.]+$/, '')}-${uuidv4()}${fileExtension}`;
        
        // Log upload options and filename
        logger.log('Uploading to Vercel Blob:', {
          filename: uniqueName,
          options: {
            access: 'public',
            handleUploadUrl: '/api/blob-upload',
          }
        });
        
        // Upload file directly to Vercel Blob with the unique name
        const blobResult = await upload(uniqueName, file, {
          access: 'public',
          handleUploadUrl: '/api/blob-upload',
        });
        
        // Send blob URL to backend for flashcard generation
        const result = await generateFlashcardsAction({
          fileUrl: blobResult.url,
          settings
        });

        if (result.success && result.data) {
          // Track successful upload for rate limiting
          await trackUserUpload();
          
          onFlashcardsGenerated(result.data.flashcards, result.data.title);
          toast({
            title: "Success!",
            description: `Generated ${result.data.flashcards.length} flashcards from ${file.name}`,
          });
        } else {
          throw new Error(result.error || 'Failed to generate flashcards');
        }
      }
    } catch (error) {
      logger.error('Error processing files:', error);
      setError(error instanceof Error ? error.message : 'Failed to process files');
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to process files',
      });
    } finally {
      setIsSubmitting(false);
      setAppIsLoading(false);
    }
  };

  // Add drag and drop handlers
  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault(); // Prevent default behavior to allow drop
    setIsDraggingOver(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    setIsDraggingOver(false);
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault(); // Prevent default behavior
    setIsDraggingOver(false);

    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      setFiles(Array.from(event.dataTransfer.files));
      setError(null); // Clear previous errors
    } else {
      setFiles([]);
    }
  };

  const handleRemoveFile = (indexToRemove: number) => {
    setFiles(currentFiles => currentFiles.filter((_, index) => index !== indexToRemove));
  };

  const renderAuthAlert = () => {
    if (!loading && !user) {
      return (
        <Alert className="mb-6 mt-1">
          <Info className="h-4 w-4" />
          <AlertTitle>Authentication Required</AlertTitle>
          <AlertDescription>
            <p>You need to <Link href="/auth/signin" className="font-medium underline">sign in</Link> to generate flashcards.</p>
          </AlertDescription>
        </Alert>
      );
    }
    return null;
  };

  const renderUsageInfo = () => {
    if (user && usageInfo) {
      return (
        <div className="text-xs text-muted-foreground mt-2 flex flex-col gap-1">
          <p className="text-sm font-medium">{usageInfo.tier} Plan</p>
          <p>Daily: {usageInfo.dailyUploads} / {usageInfo.dailyLimit} uploads used</p>
          <p>Monthly: {usageInfo.monthlyUploads} / {usageInfo.monthlyLimit} uploads used</p>
          {process.env.NEXT_PUBLIC_ENABLE_SUBSCRIPTIONS === 'true' && (
            <Link href="/subscription" className="text-xs text-primary hover:underline mt-1">
              Upgrade plan
            </Link>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="w-full flex justify-center items-center">
      <div className="w-full max-w-md p-4 sm:p-8 space-y-6 bg-card text-card-foreground rounded-lg shadow-xl mx-auto">
        <div className="text-center">
          <h2 className="text-2xl font-semibold">Upload Files</h2>
          <p className="text-sm text-muted-foreground">
            Select documents to generate flashcards.
          </p>
        </div>
        
        {renderAuthAlert()}
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <Label htmlFor="file-upload" className="sr-only">Upload Files</Label>
            <div
              className={`mt-2 flex justify-center px-4 sm:px-6 py-6 sm:py-8 border-2 border-dashed rounded-md transition-colors ${isMobile ? 'min-h-[150px]' : ''} ${isDraggingOver ? 'border-primary bg-primary/10' : 'border-border hover:border-primary'}`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <div className="space-y-3 text-center">
                <UploadCloud className="mx-auto h-12 w-12 text-muted-foreground" />
                <div className="flex flex-col sm:flex-row items-center justify-center text-sm text-muted-foreground">
                  <Label
                    htmlFor="file-upload"
                    className="relative cursor-pointer rounded-md font-medium text-primary hover:text-primary/80 focus-within:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"
                  >
                    <span className={`${isMobile ? 'text-base py-1 block' : ''}`}>
                      {isMobile ? 'Select files' : 'Upload files'}
                    </span>
                    <Input 
                      id="file-upload" 
                      name="files" 
                      type="file" 
                      multiple 
                      className="sr-only" 
                      onChange={handleFileChange} 
                      accept={ACCEPT_STRING} 
                      disabled={isSubmitting || isCheckingLimits} 
                    />
                  </Label>
                  {!isMobile && <p className="pl-1">or drag and drop</p>}
                </div>
                <p className="text-xs text-muted-foreground">Files up to 15MB each</p>
                <p className="text-xs text-muted-foreground">Supports PDF, Word, PowerPoint, TXT, and Markdown files</p>
                
                {isMobile && (
                  <div className="mt-2 flex items-center justify-center text-xs text-muted-foreground">
                    <Smartphone className="h-3 w-3 mr-1" />
                    <span>
                      On mobile, you can select files from your device or cloud storage
                    </span>
                  </div>
                )}
                
                {renderUsageInfo()}
              </div>
            </div>
            {files.length > 0 && (
              <div className="mt-4 text-sm text-muted-foreground">
                <p className="font-medium text-card-foreground mb-2">Selected file{files.length > 1 ? 's' : ''}: ({(files.reduce((sum, file) => sum + file.size, 0) / 1024 / 1024).toFixed(2)} MB / 15 MB)</p>
                <div className="max-w-full">
                  {files.map((f, index) => (
                    <div key={`${f.name}-${index}`} className="flex items-center w-full gap-2 py-1">
                      <FileText className="h-4 w-4 flex-shrink-0 text-muted-foreground" />
                      <span className="truncate max-w-[calc(100%-10rem)]" title={f.name}>
                        {f.name}
                      </span>
                      <span className="flex-shrink-0">
                        ({(f.size / 1024 / 1024).toFixed(2)} MB)
                      </span>
                      <button
                        type="button"
                        onClick={() => handleRemoveFile(index)}
                        className="ml-auto p-1 rounded-md hover:bg-muted transition-colors"
                        aria-label={`Remove file ${f.name}`}
                      >
                        <Trash2 className="h-4 w-4 text-muted-foreground hover:text-destructive" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Button 
            type="submit" 
            className="w-full py-6 text-base" 
            disabled={isSubmitting || files.length === 0 || isCheckingLimits || (!user && !loading)}
            size={isMobile ? "lg" : "default"}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Generating...
              </>
            ) : isCheckingLimits ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Checking limits...
              </>
            ) : (
              'Generate Flashcards'
            )}
          </Button>
        </form>
      </div>
    </div>
  );
}
