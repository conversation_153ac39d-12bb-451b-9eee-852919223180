import Link from 'next/link'

export function Footer() {
  return (
    <footer className="mt-12 text-center text-sm text-muted-foreground w-full">
      <div className="flex flex-col items-center gap-2">
        <div className="flex items-center gap-4 flex-wrap justify-center">
          <Link 
            href="/legal/privacy" 
            className="hover:text-foreground transition-colors"
          >
            Privacy Policy
          </Link>
          <span className="text-muted-foreground/50">•</span>
          <Link 
            href="/legal/terms" 
            className="hover:text-foreground transition-colors"
          >
            Terms & Conditions
          </Link>
        </div>
        <div className="flex items-center gap-2 flex-wrap justify-center">
          <p>&copy; {new Date().getFullYear()} Flashcard AI. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}
