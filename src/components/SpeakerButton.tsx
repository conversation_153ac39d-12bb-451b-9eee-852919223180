"use client";

import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Volume2, VolumeX, Loader2 } from 'lucide-react';
import { useAudio } from '@/hooks/use-audio';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface SpeakerButtonProps {
  text: string;
  voiceName?: string;
  className?: string;
  size?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'outline' | 'ghost' | 'secondary';
  onClick?: (e: React.MouseEvent) => void;
}

export function SpeakerButton({
  text,
  voiceName = 'Rachel',
  className = '',
  size = 'sm',
  variant = 'ghost',
  onClick
}: SpeakerButtonProps) {
  const { audioState, playText, stopAudio } = useAudio();

  const handleClick = async (e: React.MouseEvent) => {
    // Prevent event bubbling to parent elements
    if (onClick) {
      onClick(e);
    }

    if (audioState.isPlaying) {
      stopAudio();
    } else if (!audioState.isLoading) {
      await playText(text, voiceName);
    }
  };

  const getIcon = () => {
    // Use larger icons for better mobile touch targets
    const iconSize = size === 'sm' ? 'h-4 w-4' : 'h-5 w-5';

    if (audioState.isLoading) {
      return <Loader2 className={`${iconSize} animate-spin`} />;
    }
    if (audioState.isPlaying) {
      return <VolumeX className={iconSize} />;
    }
    return <Volume2 className={iconSize} />;
  };

  const getTooltipText = () => {
    if (audioState.isLoading) {
      return 'Generating natural AI voice...';
    }
    if (audioState.isPlaying) {
      return 'Stop audio';
    }
    return 'Play with AI voice';
  };

  const isDisabled = audioState.isLoading || !text || text.trim().length === 0;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            size={size}
            onClick={handleClick}
            disabled={isDisabled}
            className={`transition-all duration-200 ${
              audioState.isPlaying ? 'text-primary' : ''
            } ${className}`}
            aria-label={getTooltipText()}
          >
            {getIcon()}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-xs">{getTooltipText()}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
