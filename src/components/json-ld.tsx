import { Organization, WithContext } from 'schema-dts'

const APP_NAME = process.env.NEXT_PUBLIC_APP_NAME || 'Flash Cards AI'
const APP_DESCRIPTION = process.env.NEXT_PUBLIC_APP_DESCRIPTION || 'AI-powered flashcard generator with text-to-speech, document upload (PDF, Word, PowerPoint), topic-based generation, and smart export features. Transform your study materials into interactive flashcards instantly.'

export function OrganizationJsonLd() {
  const jsonLd: WithContext<Organization> = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: APP_NAME,
    url: process.env.NEXT_PUBLIC_APP_URL,
    logo: `${process.env.NEXT_PUBLIC_APP_URL}/og-image.png`,
    sameAs: [
      // Add social media URLs when available
    ],
    description: APP_DESCRIPTION,
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
    />
  )
}

export function SoftwareApplicationJsonLd() {
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: APP_NAME,
    description: APP_DESCRIPTION,
    url: process.env.NEXT_PUBLIC_APP_URL,
    applicationCategory: 'EducationalApplication',
    operatingSystem: 'Web Browser',
    browserRequirements: 'Requires JavaScript. Requires HTML5.',
    offers: [
      {
        '@type': 'Offer',
        name: 'Free Plan',
        price: '0',
        priceCurrency: 'USD',
        description: 'Basic flashcard generation with limited features',
      },
      {
        '@type': 'Offer',
        name: 'Pro Plan',
        price: '9.99',
        priceCurrency: 'USD',
        description: 'Full access to AI flashcard generation, text-to-speech, and export features',
      },
    ],
    featureList: [
      'AI-powered flashcard generation',
      'Document upload (PDF, Word, PowerPoint)',
      'Text-to-speech with ElevenLabs integration',
      'Topic-based flashcard creation',
      'PDF and JSON export',
      'Multiple subscription tiers',
      'Interactive study interface',
      'Voice synthesis for audio learning',
    ],
    screenshot: `${process.env.NEXT_PUBLIC_APP_URL}/og-image.png`,
    softwareVersion: '2.0',
    datePublished: '2024-01-01',
    dateModified: new Date().toISOString().split('T')[0],
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
    />
  )
}