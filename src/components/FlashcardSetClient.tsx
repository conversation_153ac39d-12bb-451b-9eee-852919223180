"use client";

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { db, collection, doc, getDoc } from '@/lib/firebase';
import { useAuth } from '@/lib/auth-context';
import { FlashcardData } from '@/types';
import { FlashcardViewer } from '@/components/FlashcardViewer';
import { ExportButton } from '@/components/ExportButton';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Loader2, Trash2, AlertTriangle, Settings } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useFlashcardService } from '@/lib/flashcard-service';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { logger } from '@/lib/logger';

interface FlashcardSetClientProps {
  id: string;
}

export function FlashcardSetClient({ id }: FlashcardSetClientProps) {
  const [flashcardSet, setFlashcardSet] = useState<{
    title: string;
    flashcards: FlashcardData[];
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const { user, loading } = useAuth();
  const { deleteUserFlashcardSet } = useFlashcardService();
  const router = useRouter();
  const { toast } = useToast();

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!loading && !user) {
      router.push('/auth/signin?redirect=/history/' + id);
    }
  }, [user, loading, router, id]);

  const fetchFlashcardSet = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      const docRef = doc(collection(db, 'flashcardSets'), id);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        const data = docSnap.data();

        // Verify this flashcard set belongs to the current user
        if (data.userId !== user.id) {
          setError("You don't have permission to view this flashcard set");
          setIsLoading(false);
          return;
        }

        setFlashcardSet({
          title: data.title,
          flashcards: data.flashcards
        });
      } else {
        setError("Flashcard set not found");
      }
    } catch (error) {
      logger.error('Error fetching flashcard set:', error);
      setError('Failed to load the flashcard set');
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load the flashcard set",
      });
    } finally {
      setIsLoading(false);
    }
  }, [user, id, toast]);

  useEffect(() => {
    if (user) {
      fetchFlashcardSet();
    }
  }, [user, fetchFlashcardSet]);

  const handleDeleteClick = () => {
    setShowDeleteConfirm(true);
  };

  const handleDeleteConfirm = async () => {
    setIsDeleting(true);
    try {
      const result = await deleteUserFlashcardSet(id);
      if (result.success) {
        toast({
          title: "Deleted",
          description: "Flashcard set has been deleted",
        });
        // Navigate back to history page
        router.push('/history');
      } else {
        throw new Error(result.error as string || 'Failed to delete flashcard set');
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete flashcard set",
      });
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(false);
  };

  if (loading || isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="mt-4 text-lg">Loading flashcard set...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4 pwa-safe-all">
        <div className="text-center p-8 bg-card rounded-lg shadow-md max-w-md mx-auto">
          <p className="text-xl text-card-foreground mb-4">{error}</p>
          <Button asChild>
            <Link href="/history">Back to History</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <main className="flex flex-col items-center min-h-screen p-4 sm:p-8 bg-background pwa-safe-all">
      <header className="mb-8 w-full max-w-4xl mx-auto overflow-x-hidden">
        <div className="flex justify-between items-center mb-4 min-w-0">
          <Link href="/history" className="flex items-center min-w-0">
            <Button variant="ghost" size="icon" className="mr-2">
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <Image
              src="/flashcard_icon.png"
              alt="Flashcard AI Logo"
              width={40}
              height={40}
            />
            <span className="ml-2 w-full min-w-0 truncate text-base sm:text-xl font-bold text-foreground">
              {flashcardSet?.title || 'Flashcard Set'}
            </span>
          </Link>

          <div className="flex items-center gap-2">
            <Link href="/settings">
              <Button
                variant="outline"
                size="sm"
                className="gap-2"
              >
                <Settings className="h-4 w-4" />
                <span className="hidden sm:inline">Settings</span>
              </Button>
            </Link>
            <Button
              variant="outline"
              size="sm"
              className="text-destructive hover:text-destructive hover:bg-destructive/10"
              onClick={handleDeleteClick}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Trash2 className="h-4 w-4 mr-2" />
              )}
            </Button>
          </div>
        </div>
      </header>

      <div className="w-full max-w-2xl mx-auto">
        {flashcardSet && flashcardSet.flashcards?.length > 0 ? (
          <div className="space-y-8 w-full">
            <FlashcardViewer flashcards={flashcardSet.flashcards} />
            <div className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-4">
              <ExportButton flashcards={flashcardSet.flashcards} title={flashcardSet.title} />
              <Button asChild variant="outline">
                <Link href="/history">Back to History</Link>
              </Button>
            </div>
          </div>
        ) : (
          <div className="text-center p-8 bg-card rounded-lg shadow-md">
            <p className="text-xl text-card-foreground mb-4">No flashcards found in this set.</p>
            <Button asChild>
              <Link href="/history">Back to History</Link>
            </Button>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Delete Flashcard Set
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete &quot;{flashcardSet?.title}&quot;? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleDeleteCancel}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Trash2 className="h-4 w-4 mr-2" />
              )}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </main>
  );
}