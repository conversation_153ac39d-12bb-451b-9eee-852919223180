"use client";

import React from 'react';
import { VoiceDropdownSelector } from './VoiceSelectionGrid';
import { type TTSVoiceName } from '@/lib/tts-voices';
import { useSubscription } from '@/hooks/use-subscription';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface EnhancedTTSVoiceSelectorProps {
  questionVoice: TTSVoiceName;
  answerVoice: TTSVoiceName;
  onQuestionVoiceChange: (voice: TTSVoiceName) => void;
  onAnswerVoiceChange: (voice: TTSVoiceName) => void;
}

export function EnhancedTTSVoiceSelector({
  questionVoice,
  answerVoice,
  onQuestionVoiceChange,
  onAnswerVoiceChange
}: EnhancedTTSVoiceSelectorProps) {
  const { isPro } = useSubscription();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          AI Voice Selection
        </CardTitle>
        <CardDescription>
          {isPro
            ? "Choose from 14 natural AI voices to personalize your flashcard learning experience. Use the dropdown to select voices and the speaker button to preview them."
            : "Choose from 14 natural AI voices to personalize your flashcard learning experience. You'll need to provide your own ElevenLabs API key to use these voices."
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Question Voice Selection */}
        <VoiceDropdownSelector
          selectedVoice={questionVoice}
          onVoiceSelect={onQuestionVoiceChange}
          label="Question Voice"
          id="question-voice-select"
        />

        {/* Answer Voice Selection */}
        <VoiceDropdownSelector
          selectedVoice={answerVoice}
          onVoiceSelect={onAnswerVoiceChange}
          label="Answer Voice"
          id="answer-voice-select"
        />

        <div className="text-sm text-muted-foreground bg-muted/50 p-4 rounded-lg">
          <h4 className="font-medium mb-2">Voice Selection Tips:</h4>
          <ul className="space-y-1 text-xs">
            <li>• <strong>Educational Focus:</strong> All voices are optimized for clear pronunciation and learning</li>
            <li>• <strong>Accent Variety:</strong> Choose from American and British accents for different learning preferences</li>
            <li>• <strong>Gender Balance:</strong> Equal selection of male and female voices for personalization</li>
            <li>• <strong>Preview First:</strong> Use the speaker button to listen to voice samples before selecting</li>
            <li>• <strong>Mix & Match:</strong> Use different voices for questions and answers to improve focus</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
