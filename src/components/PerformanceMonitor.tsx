"use client";

import React, { useEffect, useRef } from 'react';
import { logger } from '@/lib/logger';

interface PerformanceMonitorProps {
  componentName: string;
  flashcardCount: number;
  children: React.ReactNode;
}

export function PerformanceMonitor({ componentName, flashcardCount, children }: PerformanceMonitorProps) {
  const renderStartTime = useRef<number>(0);
  const mountTime = useRef<number>(0);

  useEffect(() => {
    mountTime.current = performance.now();
    
    // Log performance warning for large flashcard sets
    if (flashcardCount > 100) {
      logger.warn(`[Performance] Large flashcard set detected: ${flashcardCount} cards in ${componentName}`);
    }

    return () => {
      const unmountTime = performance.now();
      const totalLifetime = unmountTime - mountTime.current;
      
      if (totalLifetime > 1000) { // Log if component was active for more than 1 second
        logger.log(`[Performance] ${componentName} lifetime: ${totalLifetime.toFixed(2)}ms with ${flashcardCount} cards`);
      }
    };
  }, [componentName, flashcardCount]);

  useEffect(() => {
    renderStartTime.current = performance.now();
  });

  useEffect(() => {
    const renderEndTime = performance.now();
    const renderTime = renderEndTime - renderStartTime.current;
    
    // Log slow renders
    if (renderTime > 16) { // More than one frame at 60fps
      logger.warn(`[Performance] Slow render in ${componentName}: ${renderTime.toFixed(2)}ms with ${flashcardCount} cards`);
    }
    
    // Log extremely slow renders
    if (renderTime > 100) {
      logger.error(`[Performance] Very slow render in ${componentName}: ${renderTime.toFixed(2)}ms with ${flashcardCount} cards`);
    }
  });

  return <>{children}</>;
}

// Hook for measuring component render performance
export function useRenderPerformance(componentName: string, dependencies: any[] = []) {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(0);

  useEffect(() => {
    renderCount.current += 1;
    const currentTime = performance.now();
    
    if (lastRenderTime.current > 0) {
      const timeSinceLastRender = currentTime - lastRenderTime.current;
      
      // Log frequent re-renders
      if (timeSinceLastRender < 16 && renderCount.current > 5) {
        logger.warn(`[Performance] Frequent re-renders in ${componentName}: ${renderCount.current} renders`);
      }
    }
    
    lastRenderTime.current = currentTime;
  }, dependencies);

  return renderCount.current;
}

// Hook for measuring memory usage (approximate)
export function useMemoryMonitor(componentName: string, dataSize: number) {
  useEffect(() => {
    // Estimate memory usage based on data size
    const estimatedMemoryMB = (dataSize * 1000) / (1024 * 1024); // Rough estimate
    
    if (estimatedMemoryMB > 10) { // More than 10MB estimated
      logger.warn(`[Performance] High memory usage estimated in ${componentName}: ~${estimatedMemoryMB.toFixed(2)}MB for ${dataSize} items`);
    }

    // Use performance.measureUserAgentSpecificMemory if available (Chrome only)
    if ('measureUserAgentSpecificMemory' in performance) {
      (performance as any).measureUserAgentSpecificMemory()
        .then((result: any) => {
          const memoryMB = result.bytes / (1024 * 1024);
          if (memoryMB > 100) { // More than 100MB total
            logger.warn(`[Performance] High total memory usage: ${memoryMB.toFixed(2)}MB`);
          }
        })
        .catch(() => {
          // Silently fail if not supported
        });
    }
  }, [componentName, dataSize]);
}
