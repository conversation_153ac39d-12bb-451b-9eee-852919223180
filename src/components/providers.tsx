import { AuthProvider } from '@/lib/auth-context'
import { SubscriptionProvider } from '@/lib/subscription-context'
import { Inter } from 'next/font/google'

const inter = Inter({
  variable: '--font-inter',
  subsets: ['latin'],
  display: 'swap',
})

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <AuthProvider>
      <SubscriptionProvider>
        <div className={`${inter.variable} font-sans antialiased h-full bg-background text-foreground`}>
          {children}
        </div>
      </SubscriptionProvider>
    </AuthProvider>
  )
}