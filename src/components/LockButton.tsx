"use client";

import React from 'react';
import { Button } from "@/components/ui/button";
import { Lock } from 'lucide-react';
import { useRouter } from 'next/navigation';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface LockButtonProps {
  feature: string;
  className?: string;
  size?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'outline' | 'ghost' | 'secondary';
  subscriptionsEnabled: boolean;
  tooltipText?: string;
}

export function LockButton({ 
  feature,
  className = '',
  size = 'sm',
  variant = 'ghost',
  subscriptionsEnabled,
  tooltipText
}: LockButtonProps) {
  const router = useRouter();

  const getTooltipText = () => {
    if (tooltipText) return tooltipText;
    
    if (subscriptionsEnabled) {
      return `${feature} is available for PRO members only`;
    } else {
      return `${feature} feature is currently unavailable`;
    }
  };

  const handleClick = () => {
    // Only redirect to subscription page if subscriptions are enabled
    if (subscriptionsEnabled) {
      router.push('/subscription');
    }
    // If subscriptions are disabled, do nothing (button is not clickable)
  };

  const isClickable = subscriptionsEnabled;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            size={size}
            onClick={isClickable ? handleClick : undefined}
            className={`transition-all duration-200 ${
              isClickable 
                ? 'cursor-pointer hover:bg-muted/50' 
                : 'cursor-not-allowed opacity-60'
            } ${className}`}
            aria-label={getTooltipText()}
            disabled={!isClickable}
          >
            <Lock className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-xs">{getTooltipText()}</p>
          {subscriptionsEnabled && (
            <p className="text-xs text-muted-foreground mt-1">Click to upgrade</p>
          )}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
