// Simple test to verify subscription status helper functions
import { PaystackSubscription } from '@/lib/paystack-service';

// Helper functions (copied from TierManagement component for testing)
const isSubscriptionRenewing = (subscription: PaystackSubscription | null): boolean => {
  if (!subscription) return false;
  // 'active' status means the subscription will auto-renew
  return subscription.status === 'active';
};

const isSubscriptionActive = (subscription: PaystackSubscription | null): boolean => {
  if (!subscription) return false;
  // Both 'active' and 'non-renewing' subscriptions are considered active until expiration
  return subscription.status === 'active' || subscription.status === 'non-renewing';
};

// Test cases
const testCases = [
  {
    name: 'null subscription',
    subscription: null,
    expectedActive: false,
    expectedRenewing: false,
  },
  {
    name: 'active subscription',
    subscription: {
      id: 1,
      subscription_code: 'SUB_123',
      email_token: 'token123',
      amount: 2900,
      status: 'active' as const,
      plan: { name: 'Basic Plan', plan_code: 'PLN_123' },
      next_payment_date: '2024-01-01',
    },
    expectedActive: true,
    expectedRenewing: true,
  },
  {
    name: 'non-renewing subscription',
    subscription: {
      id: 2,
      subscription_code: 'SUB_456',
      email_token: 'token456',
      amount: 2900,
      status: 'non-renewing' as const,
      plan: { name: 'Basic Plan', plan_code: 'PLN_123' },
      next_payment_date: '2024-01-01',
    },
    expectedActive: true,
    expectedRenewing: false,
  },
  {
    name: 'cancelled subscription',
    subscription: {
      id: 3,
      subscription_code: 'SUB_789',
      email_token: 'token789',
      amount: 2900,
      status: 'cancelled' as const,
      plan: { name: 'Basic Plan', plan_code: 'PLN_123' },
      next_payment_date: '2024-01-01',
    },
    expectedActive: false,
    expectedRenewing: false,
  },
];

// Run tests
console.log('Testing subscription status helper functions...\n');

testCases.forEach((testCase) => {
  const isActive = isSubscriptionActive(testCase.subscription);
  const isRenewing = isSubscriptionRenewing(testCase.subscription);
  
  const activeResult = isActive === testCase.expectedActive ? '✅' : '❌';
  const renewingResult = isRenewing === testCase.expectedRenewing ? '✅' : '❌';
  
  console.log(`${testCase.name}:`);
  console.log(`  Active: ${isActive} (expected: ${testCase.expectedActive}) ${activeResult}`);
  console.log(`  Renewing: ${isRenewing} (expected: ${testCase.expectedRenewing}) ${renewingResult}`);
  console.log('');
});

export { isSubscriptionActive, isSubscriptionRenewing };
