import * as React from "react"
import { <PERSON>, EyeOff } from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "./button"

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<"input">>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm select-text", // Force text selection for iOS PWA fix
          className
        )}
        ref={ref}
        style={{
          fontSize: '16px', // Prevent iOS zoom on focus
          ...props.style
        }}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

interface PasswordInputProps extends Omit<React.ComponentProps<"input">, "type"> {
  className?: string;
}

// Global flag to ensure we only add the styles once
let stylesAdded = false;

const addPasswordInputStyles = () => {
  if (stylesAdded || typeof document === 'undefined') return;

  const style = document.createElement('style');
  style.id = 'password-input-styles';
  style.textContent = `
    input[type="password"]::-ms-reveal,
    input[type="password"]::-ms-clear {
      display: none !important;
    }
    input[type="password"]::-webkit-credentials-auto-fill-button {
      display: none !important;
      visibility: hidden !important;
      pointer-events: none !important;
    }
  `;
  document.head.appendChild(style);
  stylesAdded = true;
};

const PasswordInput = React.forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ className, ...props }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false);

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    React.useEffect(() => {
      addPasswordInputStyles();
    }, []);

    return (
      <div className="relative select-text"> {/* Force text selection for iOS PWA fix */}
        <input
          type={showPassword ? "text" : "password"}
          className={cn(
            "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 pr-10 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm select-text", // Force text selection for iOS PWA fix
            className
          )}
          ref={ref}
          style={{
            fontSize: '16px', // Prevent iOS zoom on focus
            ...props.style
          }}
          {...props}
        />
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent focus:bg-transparent"
          onClick={togglePasswordVisibility}
          aria-label={showPassword ? "Hide password" : "Show password"}
          tabIndex={-1}
        >
          {showPassword ? (
            <EyeOff className="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors" />
          ) : (
            <Eye className="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors" />
          )}
        </Button>
      </div>
    );
  }
)
PasswordInput.displayName = "PasswordInput"

export { Input, PasswordInput }
