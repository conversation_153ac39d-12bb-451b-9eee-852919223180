"use client";

import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Download, Info, FileText, Printer, Loader2 } from 'lucide-react';
import type { FlashcardData } from '@/types';
import { useToast } from '@/hooks/use-toast';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { logger } from '@/lib/logger';

interface ExportButtonProps {
  flashcards: FlashcardData[];
  title?: string;
}

export function ExportButton({ flashcards, title }: ExportButtonProps) {
  const { toast } = useToast();
  const [isMobile, setIsMobile] = useState(false);
  const [isExportingPdf, setIsExportingPdf] = useState(false);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera || '';
      const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i;
      setIsMobile(mobileRegex.test(userAgent.toLowerCase()));
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleJsonExport = () => {
    if (!flashcards || flashcards.length === 0) {
      toast({
        variant: "destructive",
        title: "No Flashcards",
        description: "There are no flashcards to export.",
      });
      return;
    }

    const jsonString = JSON.stringify(flashcards, null, 2);
    const blob = new Blob([jsonString], { type: "application/json" });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = 'flashcards.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toast({
      title: "JSON Exported!",
      description: isMobile
        ? "Flashcards saved to your downloads. You can import them later or share with others."
        : "Flashcards have been downloaded as flashcards.json.",
    });
  };

  const handlePdfExport = async () => {
    if (!flashcards || flashcards.length === 0) {
      toast({
        variant: "destructive",
        title: "No Flashcards",
        description: "There are no flashcards to export.",
      });
      return;
    }

    setIsExportingPdf(true);

    try {
      const response = await fetch('/api/export-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          flashcards,
          title,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate PDF');
      }

      // Get the PDF blob
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);

      // Create download link
      const link = document.createElement('a');
      link.href = url;
      link.download = title
        ? `${title.replace(/[^a-zA-Z0-9]/g, '_')}_flashcards.pdf`
        : 'flashcards.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "PDF Exported!",
        description: isMobile
          ? "Printable flashcards saved to your downloads. Print double-sided with 'flip on short edge' for best results."
          : "Printable flashcards have been downloaded. Use duplex printing with 'flip on short edge' setting for proper alignment.",
      });

    } catch (error) {
      logger.error('Error exporting PDF:', error);
      toast({
        variant: "destructive",
        title: "Export Failed",
        description: "Failed to generate PDF. Please try again.",
      });
    } finally {
      setIsExportingPdf(false);
    }
  };

  return (
    <div className="flex items-center">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            disabled={!flashcards || flashcards.length === 0 || isExportingPdf}
            className={isMobile ? "py-6 px-4 text-base" : ""}
            size={isMobile ? "lg" : "default"}
          >
            {isExportingPdf ? (
              <>
                <Loader2 className={`${isMobile ? 'h-5 w-5 mr-2' : 'h-4 w-4 mr-2'} animate-spin`} />
                Generating PDF...
              </>
            ) : (
              <>
                <Download className={`${isMobile ? 'h-5 w-5 mr-2' : 'h-4 w-4 mr-2'}`} />
                Export Flashcards
              </>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={handlePdfExport} disabled={isExportingPdf}>
            <Printer className="h-4 w-4 mr-2" />
            Export as PDF (Printable)
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleJsonExport} disabled={isExportingPdf}>
            <FileText className="h-4 w-4 mr-2" />
            Export as JSON (Data)
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {isMobile && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" className="ml-1">
                <Info className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs max-w-52">
                Choose PDF for printable flashcards or JSON to save/import data later.
                PDF format uses zipper layout optimized for double-sided printing with 'flip on short edge'.
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
}
