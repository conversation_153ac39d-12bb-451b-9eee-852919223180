"use client";

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Eye, EyeOff, ExternalLink, TestTube, Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { maskApiKey, testElevenLabsApiKey } from '@/lib/api-key-utils';
import Link from 'next/link';
import { logger } from '@/lib/logger';

interface ElevenLabsApiKeySettingsProps {
  currentApiKey?: string;
  onApiKeyChange: (apiKey: string) => void;
  onApiKeyTest?: (apiKey: string) => Promise<{ valid: boolean; error?: string }>;
}

export function ElevenLabsApiKeySettings({
  currentApiKey,
  onApiKeyChange,
  onApiKeyTest
}: ElevenLabsApiKeySettingsProps) {
  const [apiKey, setApiKey] = useState(currentApiKey || '');
  const [showApiKey, setShowApiKey] = useState(false);
  const [isTestingKey, setIsTestingKey] = useState(false);
  const [testResult, setTestResult] = useState<{ valid: boolean; error?: string } | null>(null);
  const { toast } = useToast();

  const handleApiKeyChange = (value: string) => {
    setApiKey(value);
    onApiKeyChange(value);
    // Clear test result when key changes
    setTestResult(null);
  };

  const handleDeleteApiKey = () => {
    handleApiKeyChange('');
    toast({
      title: "API Key Cleared",
      description: "Your ElevenLabs API key has been removed.",
    });
  };

  const handleTestApiKey = async () => {
    if (!apiKey.trim()) {
      toast({
        variant: "destructive",
        title: "No API Key",
        description: "Please enter an API key to test.",
      });
      return;
    }

    setIsTestingKey(true);
    try {
      const result = onApiKeyTest 
        ? await onApiKeyTest(apiKey.trim())
        : await testElevenLabsApiKey(apiKey.trim());
      
      setTestResult(result);
      
      if (result.valid) {
        toast({
          title: "API Key Valid",
          description: "Your ElevenLabs API key is working correctly!",
        });
      } else {
        toast({
          variant: "destructive",
          title: "API Key Invalid",
          description: result.error || "The API key is not valid.",
        });
      }
    } catch (error) {
      logger.error('Error testing API key:', error);
      toast({
        variant: "destructive",
        title: "Test Failed",
        description: "Failed to test the API key. Please try again.",
      });
    } finally {
      setIsTestingKey(false);
    }
  };

  const displayValue = showApiKey ? apiKey : (apiKey ? maskApiKey(apiKey) : '');

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          ElevenLabs API Key
          <Link
            href="/elevenlabs-setup"
            className="text-sm text-primary hover:text-primary/80 flex items-center gap-1"
          >
            <ExternalLink className="h-3 w-3" />
            Setup Guide
          </Link>
        </CardTitle>
        <CardDescription>
          Connect your ElevenLabs API key to unlock natural AI voice synthesis with 15 premium voices.
          Your API key is encrypted and stored securely.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="elevenlabs-api-key">API Key</Label>
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Input
                id="elevenlabs-api-key"
                type={showApiKey ? "text" : "password"}
                value={displayValue}
                onChange={(e) => handleApiKeyChange(e.target.value)}
                placeholder="Enter your ElevenLabs API key"
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowApiKey(!showApiKey)}
              >
                {showApiKey ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            <Button
              type="button"
              variant="outline"
              onClick={handleTestApiKey}
              disabled={!apiKey.trim() || isTestingKey}
              className="flex items-center gap-2"
            >
              {isTestingKey ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <TestTube className="h-4 w-4" />
              )}
              Test
            </Button>
            {apiKey.trim() && (
              <Button
                type="button"
                variant="outline"
                onClick={handleDeleteApiKey}
                className="flex items-center gap-2 text-destructive hover:text-destructive"
              >
                <Trash2 className="h-4 w-4" />
                Delete
              </Button>
            )}
          </div>
        </div>

        {/* Test Result */}
        {testResult && (
          <Alert variant={testResult.valid ? "default" : "destructive"}>
            <AlertDescription>
              {testResult.valid
                ? "✓ API key is valid and working!"
                : `✗ ${testResult.error || "API key is invalid"}`
              }
            </AlertDescription>
          </Alert>
        )}

        {/* Information */}
        <div className="text-sm text-muted-foreground space-y-2">
          <p>
            <strong>Need an API key?</strong> Visit the{' '}
            <Link
              href="/elevenlabs-setup"
              className="text-primary hover:text-primary/80 underline"
            >
              setup guide
            </Link>{' '}
            for step-by-step instructions.
          </p>
          <p>
            <strong>Free tier:</strong> ElevenLabs provides 10,000 characters per month for free.
          </p>
          <p>
            <strong>Security:</strong> Your API key is encrypted before storage and never logged.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
