"use client";

import React, { memo, useCallback, useRef, useEffect, useState } from 'react';
import { FixedSizeList as List } from 'react-window';
import { FlashcardData } from '@/types';
import { cn } from '@/lib/utils';
import { Check } from 'lucide-react';

interface VirtualizedFlashcardListProps {
  flashcards: FlashcardData[];
  currentCardIndex: number;
  knownCards: Set<number>;
  onSelectCard: (index: number) => void;
  height: number;
}

interface ListItemProps {
  index: number;
  style: React.CSSProperties;
  data: {
    flashcards: FlashcardData[];
    currentCardIndex: number;
    knownCards: Set<number>;
    onSelectCard: (index: number) => void;
  };
}

// Memoized component for displaying question text with proper wrapping and truncation
const QuestionText = memo(({ text }: { text: string }) => (
  <div className="flex-1 min-w-0 pr-2">
    <div
      className={cn(
        "flashcard-question-text",
        // CSS line clamping for responsive text truncation
        "line-clamp-2 sm:line-clamp-3",
        // Responsive font sizing and spacing
        "text-xs sm:text-sm",
        "leading-tight sm:leading-normal"
      )}
      title={text} // Show full text on hover for accessibility
    >
      {text}
    </div>
  </div>
));

QuestionText.displayName = 'QuestionText';

// Memoized list item component to prevent unnecessary re-renders
const ListItem = memo(({ index, style, data }: ListItemProps) => {
  const { flashcards, currentCardIndex, knownCards, onSelectCard } = data;
  const card = flashcards[index];

  const handleClick = useCallback(() => {
    onSelectCard(index);
  }, [index, onSelectCard]);

  return (
    <div style={style}>
      <div className="px-2 py-1">
        <button
          onClick={handleClick}
          className={cn(
            "text-left w-full rounded-md transition-colors",
            "py-3 px-3 sm:py-2 sm:px-3", // More padding on mobile
            "flex items-start gap-2", // Changed from items-center to items-start for proper alignment
            "hover:bg-accent/50 focus:bg-accent/50",
            currentCardIndex === index && "bg-primary/10 text-primary font-medium",
            knownCards.has(index) && "opacity-60"
          )}
        >
          <span className="text-xs text-muted-foreground mt-0.5 flex-shrink-0">
            {index + 1}.
          </span>
          <QuestionText text={card.question} />
          {knownCards.has(index) && (
            <Check className="h-4 w-4 text-green-500 flex-shrink-0 mt-0.5" />
          )}
        </button>
      </div>
    </div>
  );
});

ListItem.displayName = 'ListItem';

export const VirtualizedFlashcardList = memo(({
  flashcards,
  currentCardIndex,
  knownCards,
  onSelectCard,
  height: propHeight
}: VirtualizedFlashcardListProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerHeight, setContainerHeight] = useState(propHeight || 400);

  // Calculate item height based on content and padding
  const itemHeight = 60; // Approximate height for each item including padding

  // Measure container height dynamically
  useEffect(() => {
    const measureHeight = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        const availableHeight = rect.height;
        if (availableHeight > 0) {
          setContainerHeight(availableHeight);
        }
      }
    };

    // Initial measurement
    measureHeight();

    // Set up ResizeObserver for dynamic height updates
    const resizeObserver = new ResizeObserver(measureHeight);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    // Fallback for older browsers
    const handleResize = () => setTimeout(measureHeight, 100);
    window.addEventListener('resize', handleResize);

    return () => {
      resizeObserver.disconnect();
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Use provided height if available, otherwise use measured height
  const effectiveHeight = propHeight > 0 ? propHeight : containerHeight;

  // Prepare data for the virtualized list
  const listData = {
    flashcards,
    currentCardIndex,
    knownCards,
    onSelectCard
  };

  // Performance optimization: Only render if we have flashcards
  if (!flashcards || flashcards.length === 0) {
    return (
      <div ref={containerRef} className="p-4 text-center text-muted-foreground h-full">
        No flashcards to display
      </div>
    );
  }

  // For small lists (< 50 items), use regular rendering to avoid virtualization overhead
  if (flashcards.length < 50) {
    return (
      <div ref={containerRef} className="h-full overflow-auto">
        <div className="space-y-2 p-2">
          {flashcards.map((card, index) => (
            <div key={index}>
              <button
                onClick={() => onSelectCard(index)}
                className={cn(
                  "text-left w-full rounded-md transition-colors",
                  "py-3 px-3 sm:py-2 sm:px-3",
                  "flex items-start gap-2",
                  "hover:bg-accent/50 focus:bg-accent/50",
                  currentCardIndex === index && "bg-primary/10 text-primary font-medium",
                  knownCards.has(index) && "opacity-60"
                )}
              >
                <span className="text-xs text-muted-foreground mt-0.5 flex-shrink-0">
                  {index + 1}.
                </span>
                <QuestionText text={card.question} />
                {knownCards.has(index) && (
                  <Check className="h-4 w-4 text-green-500 flex-shrink-0 mt-0.5" />
                )}
              </button>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Use virtualization for large lists
  return (
    <div ref={containerRef} className="h-full">
      <List
        height={effectiveHeight}
        width="100%"
        itemCount={flashcards.length}
        itemSize={itemHeight}
        itemData={listData}
        overscanCount={5} // Render 5 extra items outside viewport for smooth scrolling
      >
        {ListItem}
      </List>
    </div>
  );
});

VirtualizedFlashcardList.displayName = 'VirtualizedFlashcardList';
