"use client";

import React, { useState } from 'react';
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Volume2, VolumeX, Loader2 } from 'lucide-react';
import { VOICE_METADATA, type TTSVoiceName } from '@/lib/tts-voices';
import { useAudio } from '@/hooks/use-audio';
import { cn } from '@/lib/utils';

interface VoiceDropdownSelectorProps {
  selectedVoice: TTSVoiceName;
  onVoiceSelect: (voice: TTSVoiceName) => void;
  label: string;
  id: string;
}

export function VoiceDropdownSelector({
  selectedVoice,
  onVoiceSelect,
  label,
  id
}: VoiceDropdownSelectorProps) {
  const { audioState, playAudioUrl, stopAudio } = useAudio();
  const [playingVoice, setPlayingVoice] = useState<TTSVoiceName | null>(null);

  // Group voices by gender for organized display
  const maleVoices = Object.values(VOICE_METADATA).filter(voice => voice.gender === 'male');
  const femaleVoices = Object.values(VOICE_METADATA).filter(voice => voice.gender === 'female');
  const selectedVoiceData = VOICE_METADATA[selectedVoice];

  const handlePreviewClick = async (voice: TTSVoiceName, previewUrl: string) => {
    if (playingVoice === voice && audioState.isPlaying) {
      // Stop if currently playing this voice
      stopAudio();
      setPlayingVoice(null);
    } else {
      // Stop any current audio and play new one
      stopAudio();
      setPlayingVoice(voice);

      try {
        await playAudioUrl(previewUrl);
      } catch (error) {
        console.error('Error playing preview:', error);
        setPlayingVoice(null);
      }
    }
  };

  const isVoicePlaying = (voice: TTSVoiceName) => {
    return playingVoice === voice && audioState.isPlaying;
  };

  const isVoiceLoading = (voice: TTSVoiceName) => {
    return playingVoice === voice && audioState.isLoading;
  };

  const handlePreviewSelectedVoice = () => {
    if (selectedVoiceData) {
      handlePreviewClick(selectedVoice, selectedVoiceData.preview_url);
    }
  };

  const getPreviewButtonIcon = () => {
    const isCurrentlyPlaying = isVoicePlaying(selectedVoice);
    const isCurrentlyLoading = isVoiceLoading(selectedVoice);

    // Use larger icons for better mobile touch targets
    if (isCurrentlyLoading) {
      return <Loader2 className="h-5 w-5 animate-spin" />;
    }
    if (isCurrentlyPlaying) {
      return <VolumeX className="h-5 w-5" />;
    }
    return <Volume2 className="h-5 w-5" />;
  };

  return (
    <div className="space-y-2">
      <Label htmlFor={id} className="text-sm font-medium">
        {label}
      </Label>
      <div className="flex gap-2">
        <Select value={selectedVoice} onValueChange={onVoiceSelect}>
          <SelectTrigger id={id} className="flex-1">
            <SelectValue>
              <div className="flex items-center gap-2">
                <span>{selectedVoiceData.name}</span>
                <div className="flex gap-1">
                  <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                    {selectedVoiceData.gender}
                  </Badge>
                  <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                    {selectedVoiceData.accent}
                  </Badge>
                </div>
              </div>
            </SelectValue>
          </SelectTrigger>
          <SelectContent className="max-h-80">
            {/* Male Voices Group */}
            <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground border-b">
              Male Voices ({maleVoices.length})
            </div>
            {maleVoices.map((voice) => (
              <SelectItem key={voice.name} value={voice.name} className="py-3">
                <div className="flex flex-col gap-1 w-full">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-sm">{voice.name}</span>
                    <div className="flex gap-1">
                      <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                        {voice.gender}
                      </Badge>
                      <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                        {voice.accent}
                      </Badge>
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {voice.description} • {voice.age} • {voice.use_case}
                  </div>
                </div>
              </SelectItem>
            ))}

            {/* Female Voices Group */}
            <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground border-b border-t mt-1">
              Female Voices ({femaleVoices.length})
            </div>
            {femaleVoices.map((voice) => (
              <SelectItem key={voice.name} value={voice.name} className="py-3">
                <div className="flex flex-col gap-1 w-full">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-sm">{voice.name}</span>
                    <div className="flex gap-1">
                      <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                        {voice.gender}
                      </Badge>
                      <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                        {voice.accent}
                      </Badge>
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {voice.description} • {voice.age} • {voice.use_case}
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Preview Button */}
        <Button
          variant="outline"
          size="default"
          onClick={handlePreviewSelectedVoice}
          disabled={isVoiceLoading(selectedVoice)}
          className="flex-shrink-0 px-3"
          title={`Preview ${selectedVoiceData.name} voice`}
        >
          {getPreviewButtonIcon()}
        </Button>
      </div>

      {/* Selected Voice Details */}
      <div className="text-xs text-muted-foreground bg-muted/30 p-2 rounded-md">
        <span className="font-medium">{selectedVoiceData.name}:</span> {selectedVoiceData.description} • {selectedVoiceData.age} • {selectedVoiceData.use_case}
      </div>
    </div>
  );
}
