export interface FlashcardData {
  question: string;
  answer: string;
  id?: number; // Optional identifier for the flashcard
}

export interface UserSettings {
  answerDetail: number; // 0-100 scale for answer detail level
  cardRange: {
    min: number; // Minimum number of cards to generate
    max: number; // Maximum number of cards to generate
  };
  ttsVoices?: {
    questionVoice: string; // Voice for questions (default: "<PERSON>")
    answerVoice: string;   // Voice for answers (default: "Adam")
  };
  elevenLabsApiKey?: string; // User's own ElevenLabs API key (for FREE/BASIC users)
}

export interface TopicFlashcardsInput {
  topic: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  subject?: string;
  settings: UserSettings;
}

export type GenerationMode = 'document' | 'topic';
