/**
 * This file contains the prompt template for topic-based flashcard generation.
 * It exports functions that return prompts for generating flashcards from general topics.
 */

import { UserSettings } from '@/types';
import { getDetailLevelInstructions } from './flashcard-prompt';

/**
 * Returns difficulty-specific instructions
 */
export const getDifficultyInstructions = (difficulty: string): string => {
  switch (difficulty) {
    case 'beginner':
      return "Focus on fundamental concepts, basic definitions, and introductory-level information. Use simple language and avoid complex terminology unless necessary. Include foundational knowledge that builds understanding.";
    case 'intermediate':
      return "Include both fundamental and more advanced concepts. Use appropriate technical terminology with explanations. Cover practical applications and connections between concepts.";
    case 'advanced':
      return "Focus on complex concepts, advanced applications, and nuanced understanding. Use technical terminology appropriately. Include challenging questions that test deep comprehension and critical thinking.";
    default:
      return "Cover a range of concepts from basic to intermediate level, ensuring comprehensive understanding of the topic.";
  }
};

/**
 * Returns subject-specific instructions
 */
export const getSubjectInstructions = (subject: string): string => {
  switch (subject?.toLowerCase()) {
    case 'science':
    case 'biology':
    case 'chemistry':
    case 'physics':
      return "Include scientific principles, processes, formulas where relevant, and real-world applications. Focus on cause-and-effect relationships and experimental evidence.";
    case 'mathematics':
    case 'math':
      return "Include step-by-step problem-solving approaches, formulas, theorems, and practical examples. Show mathematical reasoning and applications.";
    case 'history':
      return "Include important dates, key figures, causes and effects of events, and historical significance. Provide context and connections between events.";
    case 'literature':
    case 'english':
    case 'language arts':
      return "Include literary devices, themes, character analysis, and contextual information. Focus on interpretation and critical analysis skills.";
    case 'geography':
      return "Include locations, physical features, climate patterns, and human-environment interactions. Focus on spatial relationships and regional characteristics.";
    case 'economics':
    case 'business':
      return "Include economic principles, market mechanisms, business concepts, and real-world applications. Focus on cause-and-effect relationships in economic systems.";
    case 'psychology':
      return "Include psychological theories, research findings, key figures, and practical applications. Focus on human behavior and mental processes.";
    case 'computer science':
    case 'programming':
      return "Include algorithms, programming concepts, data structures, and practical coding examples. Focus on problem-solving approaches and technical implementation.";
    case 'general':
    case '':
    default:
      return "Adapt the content style to the subject matter, using appropriate terminology and focusing on key concepts, principles, and applications relevant to the field.";
  }
};

/**
 * Constructs the topic-based flashcard generation prompt with user settings
 */
export const buildTopicFlashcardPrompt = (
  topic: string,
  difficulty: string = 'intermediate',
  subject: string = '',
  settings: UserSettings
): string => {
  const detailInstructions = getDetailLevelInstructions(settings.answerDetail);
  const difficultyInstructions = getDifficultyInstructions(difficulty);
  const subjectInstructions = getSubjectInstructions(subject);

  return `You are an expert educator skilled at creating comprehensive flashcards for studying any topic.

Create a thorough set of flashcards about: "${topic}"

${difficultyInstructions}

${subjectInstructions}

Please follow these guidelines:
1. CREATE A DESCRIPTIVE TITLE for the flashcard set based on the topic.
2. GENERATE BETWEEN ${settings.cardRange.min} AND ${settings.cardRange.max} FLASHCARDS covering the most important aspects of this topic.
3. Cover fundamental concepts, key definitions, important facts, processes, and practical applications related to the topic.
4. Create a variety of question types:
   - Definition questions ("What is...?")
   - Example questions ("Give an example of...")
   - Application questions ("How would you use...?")
   - Comparison questions ("What's the difference between...?")
   - Process questions ("What are the steps to...?")
5. ANSWER DETAIL LEVEL: ${detailInstructions}
6. Ensure questions progress logically from basic concepts to more complex applications.
7. Include real-world examples and practical applications where relevant.
8. Make questions specific and focused rather than overly broad.
9. Ensure answers are accurate, complete, and educational.
10. STRICTLY return only the JSON object. DO NOT include any markdown formatting, code fences (like \`\`\`), or any other leading or trailing text.

Return a JSON object with the format: {"title": "Descriptive title for the set", "flashcards": [{"question": "question text", "answer": "detailed answer text"}, ...]}.`;
};
