'use server';

/**
 * @fileOverview This file defines a Genkit flow to generate flashcards from a document or image.
 *
 * - generateFlashcards - A function that handles the flashcard generation process.
 * - GenerateFlashcardsInput - The input type for the generateFlashcards function.
 * - GenerateFlashcardsOutput - The return type for the generateFlashcards function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';
import {
  buildFlashcardPrompt,
  getFlashcardSafetySettings
} from '@/ai/prompts';
import { logger } from '@/lib/logger';

const GenerateFlashcardsInputSchema = z.object({
  fileUrl: z
    .string()
    .describe(
      "A URL to a document or image stored in blob storage. Expected format: 'https://...'."
    ),
  settings: z.object({
    answerDetail: z.number().min(0).max(100).describe('The desired level of detail in answers (0-100)'),
    cardRange: z.object({
      min: z.number().min(3).max(100).describe('Minimum number of cards to generate'),
      max: z.number().min(3).max(100).describe('Maximum number of cards to generate')
    })
  }).describe('User preferences for flashcard generation')
});

export type GenerateFlashcardsInput = z.infer<typeof GenerateFlashcardsInputSchema>;

const FlashcardSchema = z.object({
  question: z.string().describe('The question on the flashcard.'),
  answer: z.string().describe('The answer on the flashcard.'),
});

const GenerateFlashcardsOutputSchema = z.object({
  title: z.string().describe('A descriptive title for the flashcard set, based on the document content.'),
  flashcards: z.array(FlashcardSchema).describe('An array of flashcards generated from the document content.'),
});

export type GenerateFlashcardsOutput = z.infer<typeof GenerateFlashcardsOutputSchema>;

export async function generateFlashcards(input: GenerateFlashcardsInput): Promise<GenerateFlashcardsOutput> {
  return generateFlashcardsFlow(input);
}

const prompt = ai.definePrompt({
  name: 'generateFlashcardsPrompt',
  input: {schema: GenerateFlashcardsInputSchema},
  output: {schema: GenerateFlashcardsOutputSchema},
  config: {
    safetySettings: getFlashcardSafetySettings(),
  },
});

const generateFlashcardsFlow = ai.defineFlow(
  {
    name: 'generateFlashcardsFlow',
    inputSchema: GenerateFlashcardsInputSchema,
    outputSchema: GenerateFlashcardsOutputSchema,
  },
  async input => {
    // Log for debugging (truncating the file URL)
    logger.log('Processing input:', {
      fileUrl: input.fileUrl.slice(0, 50) + '...',
      settings: input.settings
    });

    // Build the prompt using the user's settings
    const dynamicPrompt = buildFlashcardPrompt(input.fileUrl, input.settings);
    logger.log('Sending prompt to AI:', dynamicPrompt);
    // Use the ai.generate method directly for dynamic prompts
    const { text } = await ai.generate({
      prompt: dynamicPrompt,
    });
    logger.log('Raw AI response:', text);
    
    let jsonString = text;
    const jsonStartIndex = jsonString.indexOf('{');
    const jsonEndIndex = jsonString.lastIndexOf('}');

    // If object not found, try array
    if (jsonStartIndex === -1) {
      const arrayStartIndex = jsonString.indexOf('[');
      const arrayEndIndex = jsonString.lastIndexOf(']');
      if (arrayStartIndex !== -1) {
        jsonString = jsonString.substring(arrayStartIndex, arrayEndIndex + 1);
      } else {
        // If neither object nor array found, perhaps it's just text or an error
        logger.error('AI response did not contain a valid JSON object or array:', text);
        throw new Error('AI response did not contain expected JSON format.');
      }
    } else {
      jsonString = jsonString.substring(jsonStartIndex, jsonEndIndex + 1);
    }

    // Sanitize: replace escaped newlines and carriage returns
    jsonString = jsonString.replace(/\\n/g, '\n').replace(/\\r/g, '\r');

    logger.log('Extracted JSON string:', jsonString);

    try {
      const output = JSON.parse(jsonString);
      return output;
    } catch (parseError) {
      logger.error('Failed to parse AI response as JSON:', {
        error: parseError,
        problematicString: jsonString,
      });
      throw new Error('Failed to parse AI response. The AI may have returned invalid JSON.');
    }
  }
);
