'use server';

/**
 * @fileOverview This file defines a Genkit flow to generate flashcards from a topic.
 *
 * - generateTopicFlashcards - A function that handles the topic-based flashcard generation process.
 * - GenerateTopicFlashcardsInput - The input type for the generateTopicFlashcards function.
 * - GenerateTopicFlashcardsOutput - The return type for the generateTopicFlashcards function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';
import {
  buildTopicFlashcardPrompt,
  getFlashcardSafetySettings
} from '@/ai/prompts';
import { logger } from '@/lib/logger';

const GenerateTopicFlashcardsInputSchema = z.object({
  topic: z
    .string()
    .min(3)
    .max(500)
    .describe("The topic or subject to generate flashcards about"),
  difficulty: z
    .enum(['beginner', 'intermediate', 'advanced'])
    .optional()
    .describe('The difficulty level for the flashcards'),
  subject: z
    .string()
    .optional()
    .describe('The subject area or field of study'),
  settings: z.object({
    answerDetail: z.number().min(0).max(100).describe('The desired level of detail in answers (0-100)'),
    cardRange: z.object({
      min: z.number().min(3).max(100).describe('Minimum number of cards to generate'),
      max: z.number().min(3).max(100).describe('Maximum number of cards to generate')
    })
  }).describe('User preferences for flashcard generation')
});

export type GenerateTopicFlashcardsInput = z.infer<typeof GenerateTopicFlashcardsInputSchema>;

const FlashcardSchema = z.object({
  question: z.string().describe('The question on the flashcard.'),
  answer: z.string().describe('The answer on the flashcard.'),
});

const GenerateTopicFlashcardsOutputSchema = z.object({
  title: z.string().describe('A descriptive title for the flashcard set, based on the topic.'),
  flashcards: z.array(FlashcardSchema).describe('An array of flashcards generated about the topic.'),
});

export type GenerateTopicFlashcardsOutput = z.infer<typeof GenerateTopicFlashcardsOutputSchema>;

export async function generateTopicFlashcards(input: GenerateTopicFlashcardsInput): Promise<GenerateTopicFlashcardsOutput> {
  return generateTopicFlashcardsFlow(input);
}

const generateTopicFlashcardsFlow = ai.defineFlow(
  {
    name: 'generateTopicFlashcardsFlow',
    inputSchema: GenerateTopicFlashcardsInputSchema,
    outputSchema: GenerateTopicFlashcardsOutputSchema,
  },
  async input => {
    // Log for debugging
    logger.log('Processing topic input:', {
      topic: input.topic,
      difficulty: input.difficulty,
      subject: input.subject,
      settings: input.settings
    });

    // Build the prompt using the user's settings and topic
    const dynamicPrompt = buildTopicFlashcardPrompt(
      input.topic, 
      input.difficulty || 'intermediate',
      input.subject || '',
      input.settings
    );
    logger.log('Sending topic prompt to AI:', dynamicPrompt);
    
    // Use the ai.generate method directly for dynamic prompts
    const { text } = await ai.generate({
      prompt: dynamicPrompt,
      config: {
        safetySettings: getFlashcardSafetySettings(),
      },
    });
    logger.log('Raw AI response:', text);
    
    let jsonString = text;
    const jsonStartIndex = jsonString.indexOf('{');
    const jsonEndIndex = jsonString.lastIndexOf('}');

    // If object not found, try array
    if (jsonStartIndex === -1) {
      const arrayStartIndex = jsonString.indexOf('[');
      const arrayEndIndex = jsonString.lastIndexOf(']');
      if (arrayStartIndex !== -1) {
        jsonString = jsonString.substring(arrayStartIndex, arrayEndIndex + 1);
      } else {
        // If neither object nor array found, perhaps it's just text or an error
        logger.error('AI response did not contain a valid JSON object or array:', text);
        throw new Error('AI response did not contain expected JSON format.');
      }
    } else {
      jsonString = jsonString.substring(jsonStartIndex, jsonEndIndex + 1);
    }

    // Sanitize: replace escaped newlines and carriage returns
    jsonString = jsonString.replace(/\\n/g, '\n').replace(/\\r/g, '\r');

    logger.log('Extracted JSON string:', jsonString);

    try {
      const output = JSON.parse(jsonString);
      return output;
    } catch (parseError) {
      logger.error('Failed to parse AI response as JSON:', {
        error: parseError,
        problematicString: jsonString,
      });
      throw new Error('Failed to parse AI response. The AI may have returned invalid JSON.');
    }
  }
);
