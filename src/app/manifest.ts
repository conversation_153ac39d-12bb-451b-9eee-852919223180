import { MetadataRoute } from 'next'

export default function manifest(): MetadataRoute.Manifest {
  const APP_NAME = process.env.NEXT_PUBLIC_APP_NAME || 'Flash Cards AI'
  const APP_DESCRIPTION = process.env.NEXT_PUBLIC_APP_DESCRIPTION || 'AI-powered flashcard generator with text-to-speech, document upload (PDF, Word, PowerPoint), topic-based generation, and smart export features. Transform your study materials into interactive flashcards instantly.'

  return {
    name: APP_NAME,
    short_name: 'Flash Cards',
    description: APP_DESCRIPTION,
    start_url: '/',
    display: 'standalone',
    background_color: '#ffffff',
    theme_color: '#000000',
    orientation: 'portrait',
    scope: '/',
    categories: ['education', 'productivity', 'utilities'],
    lang: 'en',
    icons: [
      {
        src: '/icon-192x192.png',
        sizes: '192x192',
        type: 'image/png',
        purpose: 'any',
      },
      {
        src: '/icon-512x512.png',
        sizes: '512x512',
        type: 'image/png',
        purpose: 'any',
      },
      {
        src: '/icon-192x192.png',
        sizes: '192x192',
        type: 'image/png',
        purpose: 'maskable',
      },
      {
        src: '/icon-512x512.png',
        sizes: '512x512',
        type: 'image/png',
        purpose: 'maskable',
      },
    ],
    screenshots: [
      {
        src: '/og-image.png',
        sizes: '1200x630',
        type: 'image/png',
        form_factor: 'wide',
        label: 'Flash Cards AI - Main Interface'
      }
    ],
  }
}