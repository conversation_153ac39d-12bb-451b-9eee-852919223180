"use client";

import React, { useState, useMemo } from 'react';
import { FlashcardViewer } from '@/components/FlashcardViewer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FlashcardData } from '@/types';
import { getPerformanceTier, estimateMemoryUsage, getPerformanceRecommendations } from '@/lib/performance-utils';

// Generate test flashcards with varying complexity
function generateTestFlashcards(count: number): FlashcardData[] {
  const topics = [
    'Mathematics', 'Science', 'History', 'Literature', 'Geography', 
    'Physics', 'Chemistry', 'Biology', 'Computer Science', 'Philosophy'
  ];
  
  const questionTypes = [
    'What is',
    'How does',
    'Why is',
    'When did',
    'Where is',
    'Who was',
    'Which of the following',
    'Explain the concept of',
    'Define',
    'Compare and contrast'
  ];

  return Array.from({ length: count }, (_, index) => {
    const topic = topics[index % topics.length];
    const questionType = questionTypes[index % questionTypes.length];
    const complexity = Math.floor(index / 20) + 1; // Increase complexity every 20 cards
    
    const question = `${questionType} ${topic.toLowerCase()} concept #${index + 1}?${
      complexity > 2 ? ` This is a more complex question that requires deeper understanding of ${topic.toLowerCase()} principles and their applications in real-world scenarios.` : ''
    }`;
    
    const answer = `This is the answer for ${topic} question #${index + 1}. ${
      complexity > 1 ? `It involves understanding multiple concepts including fundamental principles, practical applications, and theoretical frameworks. ` : ''
    }${
      complexity > 3 ? `Advanced topics may include interdisciplinary connections, historical context, and modern developments in the field. Students should be able to analyze, synthesize, and evaluate information from multiple sources.` : ''
    }`;

    return {
      id: index,
      question,
      answer
    };
  });
}

export default function PerformanceTestPage() {
  const [flashcardCount, setFlashcardCount] = useState(50);
  const [showViewer, setShowViewer] = useState(false);
  const [renderTime, setRenderTime] = useState<number | null>(null);

  // Generate flashcards based on count
  const flashcards = useMemo(() => {
    const startTime = performance.now();
    const cards = generateTestFlashcards(flashcardCount);
    const endTime = performance.now();
    console.log(`Generated ${flashcardCount} flashcards in ${(endTime - startTime).toFixed(2)}ms`);
    return cards;
  }, [flashcardCount]);

  // Performance metrics
  const performanceTier = getPerformanceTier(flashcardCount);
  const memoryEstimate = estimateMemoryUsage(flashcards);
  const recommendations = getPerformanceRecommendations(flashcardCount);

  const handleShowViewer = () => {
    const startTime = performance.now();
    setShowViewer(true);
    
    // Measure render time after next tick
    setTimeout(() => {
      const endTime = performance.now();
      setRenderTime(endTime - startTime);
    }, 0);
  };

  const presetCounts = [10, 25, 50, 100, 150, 200, 300];

  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Flashcard Performance Test</h1>
        <p className="text-muted-foreground">
          Test the performance optimizations with different flashcard set sizes
        </p>
      </div>

      {!showViewer ? (
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Controls */}
          <Card>
            <CardHeader>
              <CardTitle>Test Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Number of Flashcards: {flashcardCount}
                </label>
                <input
                  type="range"
                  min="10"
                  max="500"
                  step="10"
                  value={flashcardCount}
                  onChange={(e) => setFlashcardCount(Number(e.target.value))}
                  className="w-full"
                />
              </div>
              
              <div className="flex flex-wrap gap-2">
                <span className="text-sm text-muted-foreground">Quick presets:</span>
                {presetCounts.map(count => (
                  <Button
                    key={count}
                    variant="outline"
                    size="sm"
                    onClick={() => setFlashcardCount(count)}
                    className={flashcardCount === count ? 'bg-primary text-primary-foreground' : ''}
                  >
                    {count}
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Performance Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Analysis</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-primary">{performanceTier.replace('_', ' ')}</div>
                  <div className="text-sm text-muted-foreground">Performance Tier</div>
                </div>
                
                <div className="text-center p-4 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-primary">{memoryEstimate.toFixed(1)} MB</div>
                  <div className="text-sm text-muted-foreground">Estimated Memory</div>
                </div>
                
                <div className="text-center p-4 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-primary">{flashcards.length}</div>
                  <div className="text-sm text-muted-foreground">Generated Cards</div>
                </div>
              </div>

              {recommendations.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Performance Recommendations:</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                    {recommendations.map((rec, index) => (
                      <li key={index}>{rec}</li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Start Test */}
          <div className="text-center">
            <Button onClick={handleShowViewer} size="lg">
              Start Performance Test
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Performance Results */}
          {renderTime !== null && (
            <Card>
              <CardContent className="p-4">
                <div className="flex justify-between items-center">
                  <span>Render Time: <strong>{renderTime.toFixed(2)}ms</strong></span>
                  <span>Cards: <strong>{flashcardCount}</strong></span>
                  <span>Tier: <strong>{performanceTier.replace('_', ' ')}</strong></span>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      setShowViewer(false);
                      setRenderTime(null);
                    }}
                  >
                    Reset Test
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Flashcard Viewer */}
          <FlashcardViewer flashcards={flashcards} />
        </div>
      )}
    </div>
  );
}
