"use server";

import { generateFlashcards } from '@/ai/flows/generate-flashcards';
import { generateTopicFlashcards } from '@/ai/flows/generate-topic-flashcards';
import { logger } from '@/lib/logger';
import type { FlashcardData, UserSettings, TopicFlashcardsInput } from '@/types';

interface GenerateFlashcardsActionInput {
  fileUrl: string;
  settings: UserSettings;
}

interface GenerateFlashcardsActionResult {
  success: boolean;
  data?: {
    flashcards: FlashcardData[];
    title: string;
  };
  error?: string;
}

export async function generateFlashcardsAction(
  input: GenerateFlashcardsActionInput
): Promise<GenerateFlashcardsActionResult> {
  try {
    const result = await generateFlashcards(input);
    return {
      success: true,
      data: {
        flashcards: result.flashcards,
        title: result.title
      }
    };
  } catch (error) {
    logger.error('Error generating flashcards:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate flashcards'
    };
  }
}

export async function generateTopicFlashcardsAction(
  input: TopicFlashcardsInput
): Promise<GenerateFlashcardsActionResult> {
  try {
    const result = await generateTopicFlashcards(input);
    return {
      success: true,
      data: {
        flashcards: result.flashcards,
        title: result.title
      }
    };
  } catch (error) {
    logger.error('Error generating topic flashcards:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate topic flashcards'
    };
  }
}

