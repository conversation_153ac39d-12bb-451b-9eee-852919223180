"use client";

import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ChevronLeft, ExternalLink, Key, CreditCard, Shield, HelpCircle } from 'lucide-react';
import Link from 'next/link';

export default function ElevenLabsSetupPage() {
  return (
    <main className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Link href="/settings">
          <Button variant="outline" size="sm">
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back to Settings
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">ElevenLabs AI Voice Setup</h1>
          <p className="text-muted-foreground">
            Connect your ElevenLabs API key to unlock premium AI voice synthesis for your flashcards
          </p>
        </div>
      </div>

      {/* Quick Start Alert */}
      <Alert className="mb-6">
        <Key className="h-4 w-4" />
        <AlertDescription>
          <strong>Quick Start:</strong> Create a free ElevenLabs account, go to your profile settings,
          generate an API key, and paste it in the settings page. You'll get 10,000 characters of premium AI voice generation per month for free!
        </AlertDescription>
      </Alert>

      <div className="space-y-6">
        {/* Step 1: Create Account */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm">1</span>
              Create an ElevenLabs Account
            </CardTitle>
            <CardDescription>
              Sign up for a free ElevenLabs account to access premium AI voice technology
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>Visit <a href="https://elevenlabs.io" target="_blank" rel="noopener noreferrer" className="text-primary hover:text-primary/80 underline">elevenlabs.io</a></li>
              <li>Click "Sign Up" in the top right corner</li>
              <li>Create your account using email or Google/GitHub</li>
              <li>Verify your email address if required</li>
            </ol>
            <Button asChild variant="outline" className="w-full">
              <a href="https://elevenlabs.io" target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                Go to ElevenLabs
              </a>
            </Button>
          </CardContent>
        </Card>

        {/* Step 2: Navigate to API Keys */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm">2</span>
              Access Your API Keys
            </CardTitle>
            <CardDescription>
              Find the API key section in your account settings
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>Log in to your ElevenLabs account</li>
              <li>Click on your profile picture or avatar in the top right</li>
              <li>Select "Profile" or "Settings" from the dropdown menu</li>
              <li>Look for "API Keys" or "API" section in the sidebar</li>
            </ol>
            <Alert>
              <AlertDescription>
                <strong>Alternative:</strong> You can also go directly to{' '}
                <a href="https://elevenlabs.io/app/speech-synthesis" target="_blank" rel="noopener noreferrer" className="text-primary hover:text-primary/80 underline">
                  elevenlabs.io/app/speech-synthesis
                </a>{' '}
                and look for the API key section.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Step 3: Generate API Key */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm">3</span>
              Generate Your API Key
            </CardTitle>
            <CardDescription>
              Create a new API key with text-to-speech permissions to enable premium AI voice synthesis in your flashcards
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>In the API Keys section, click "Create API Key" or "Generate New Key"</li>
              <li>Give your key a descriptive name (e.g., "Flashcard App")</li>
              <li><strong>Important:</strong> When prompted to select permissions, make sure to enable <strong>"Text-to-Speech"</strong> access</li>
              <li>Copy the generated API key immediately (you won't be able to see it again)</li>
              <li>Store it safely - you'll paste it into the settings page</li>
            </ol>
            <Alert>
              <AlertDescription>
                <strong>New Requirement:</strong> ElevenLabs now requires you to specify which features your API key can access.
                Make sure to select "Text-to-Speech" permissions when creating your key, otherwise the API key test will fail.
              </AlertDescription>
            </Alert>
            <Alert variant="destructive">
              <AlertDescription>
                <strong>Important:</strong> Copy your API key immediately after generation.
                ElevenLabs will only show it once for security reasons.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Step 4: Add to Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm">4</span>
              Add Key to Settings
            </CardTitle>
            <CardDescription>
              Configure the API key to activate premium AI voice features in your flashcard app
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>Return to the <Link href="/settings" className="text-primary hover:text-primary/80 underline">Settings page</Link></li>
              <li>Find the "ElevenLabs API Key" section</li>
              <li>Paste your API key into the input field</li>
              <li>Click "Test" to verify the key works</li>
              <li>Save your settings</li>
            </ol>
            <Button asChild className="w-full">
              <Link href="/settings">
                Go to Settings
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Pricing Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Pricing & Usage
            </CardTitle>
            <CardDescription>
              Understand ElevenLabs pricing and free tier limits
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-semibold text-accent-foreground">Free Tier</h4>
                <ul className="text-sm space-y-1">
                  <li>• 10,000 characters per month</li>
                  <li>• 3 custom voices</li>
                  <li>• All voice effects</li>
                  <li>• Commercial license</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-primary">Paid Plans</h4>
                <ul className="text-sm space-y-1">
                  <li>• Starting at $5/month</li>
                  <li>• 30,000+ characters</li>
                  <li>• More custom voices</li>
                  <li>• Priority processing</li>
                </ul>
              </div>
            </div>
            <Alert>
              <AlertDescription>
                The free tier is usually sufficient for personal flashcard use. 
                A typical flashcard uses 10-50 characters, so you can generate audio for hundreds of cards per month.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Security Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Security & Privacy
            </CardTitle>
            <CardDescription>
              How your API key is protected
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <ul className="text-sm space-y-2">
              <li className="flex items-start gap-2">
                <span className="text-accent-foreground mt-0.5">✓</span>
                Your API key is encrypted before being stored in our database
              </li>
              <li className="flex items-start gap-2">
                <span className="text-accent-foreground mt-0.5">✓</span>
                API keys are never logged or exposed in error messages
              </li>
              <li className="flex items-start gap-2">
                <span className="text-accent-foreground mt-0.5">✓</span>
                Only you can see and modify your API key
              </li>
              <li className="flex items-start gap-2">
                <span className="text-accent-foreground mt-0.5">✓</span>
                You can remove or change your API key at any time
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* Troubleshooting */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5" />
              Troubleshooting
            </CardTitle>
            <CardDescription>
              Common issues and solutions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div>
                <h4 className="font-semibold text-sm">API Key Test Fails</h4>
                <p className="text-sm text-muted-foreground">
                  Make sure you copied the entire key, that your ElevenLabs account is active, and that your API key has <strong>text-to-speech permissions</strong>.
                  If you created your API key before ElevenLabs introduced scoped permissions, you may need to generate a new one with the correct permissions.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-sm">Audio Generation Fails</h4>
                <p className="text-sm text-muted-foreground">
                  Check if you've exceeded your monthly character limit, if your API key has expired, or if your API key lacks text-to-speech permissions.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-sm">Can't Find API Keys Section</h4>
                <p className="text-sm text-muted-foreground">
                  Try going directly to your ElevenLabs dashboard and looking for "Profile" or "Settings" in the navigation.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-sm">"Invalid API key or insufficient permissions" Error</h4>
                <p className="text-sm text-muted-foreground">
                  This usually means your API key doesn't have text-to-speech permissions. Generate a new API key and make sure to select "Text-to-Speech" when choosing permissions.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </main>
  );
}
