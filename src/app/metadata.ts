import { Metadata, ResolvingMetadata } from 'next'

interface OpenGraphImage {
  url: string
  width: number
  height: number
  alt: string
}

const APP_NAME = process.env.NEXT_PUBLIC_APP_NAME || 'Flash Cards AI'
const APP_DESCRIPTION = process.env.NEXT_PUBLIC_APP_DESCRIPTION || 'AI-powered flashcard generator with text-to-speech, document upload (PDF, Word, PowerPoint), topic-based generation, and smart export features. Transform your study materials into interactive flashcards instantly.'
const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'https://www.ai-flashcards.co.za/'

const defaultOpenGraph: Metadata['openGraph'] = {
  type: 'website',
  siteName: APP_NAME,
  title: `${APP_NAME} - AI-Powered Flashcard Generator`,
  description: APP_DESCRIPTION,
  images: [
    {
      url: '/og-image.png',
      width: 1200,
      height: 630,
      alt: `${APP_NAME} - AI-Powered Flashcard Generator`
    }
  ]
}

const defaultTwitter: Metadata['twitter'] = {
  card: 'summary_large_image',
  title: `${APP_NAME} - AI-Powered Flashcard Generator`,
  description: APP_DESCRIPTION,
  images: ['/og-image.png']
}

export const defaultMetadata: Metadata = {
  metadataBase: new URL(BASE_URL),
  title: `${APP_NAME} - AI-Powered Flashcard Generator`,
  description: APP_DESCRIPTION,
  keywords: [
    'flashcards',
    'AI flashcards',
    'AI voice synthesis',
    'natural speech',
    'ElevenLabs AI',
    'AI voice flashcards',
    'audio learning',
    'study tools',
    'document conversion',
    'learning aids',
    'PDF to flashcards',
    'Word to flashcards',
    'PowerPoint to flashcards',
    'educational technology',
    'spaced repetition',
    'study aids',
    'document processing',
    'export flashcards',
    'PDF export',
    'JSON export',
    'subscription learning',
    'premium study tools',
    'topic generation',
    'flashcard generator',
    'AI study assistant',
    'AI voice generation',
    'premium audio flashcards',
    'study automation',
    'interactive learning',
    'smart study tools'
  ],
  authors: [{ name: APP_NAME }],
  creator: APP_NAME,
  publisher: APP_NAME,
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: defaultOpenGraph,
  twitter: defaultTwitter,
  icons: {
    icon: [
      { url: '/favicon.ico', type: 'image/x-icon' },
      { url: '/icon-192x192.png', type: 'image/png', sizes: '192x192' },
    ],
    apple: '/apple-touch-icon.png',
    shortcut: '/favicon.ico',
  },
}

// Export viewport separately to fix the warning
// Note: iOS PWA requires specific viewport configuration for proper keyboard behavior and safe areas
export const viewport = {
  width: 'device-width',
  initialScale: 1,
  minimumScale: 1,
  // For iOS PWA: allow scaling to enable keyboard but prevent excessive zoom
  maximumScale: 5,
  userScalable: true,
  // iOS PWA specific settings - viewport-fit=cover enables safe area insets
  viewportFit: 'cover' as const,
  themeColor: '#000000',
}

export const constructMetadata = async (
  params: {
    title?: string
    description?: string
    image?: string
    icons?: Metadata['icons']
    noIndex?: boolean
  } = {},
  _parent?: ResolvingMetadata
): Promise<Metadata> => {
  const {
    title = defaultMetadata.title as string,
    description = defaultMetadata.description as string,
    image = (defaultOpenGraph.images as OpenGraphImage[])[0].url,
    icons = defaultMetadata.icons,
    noIndex = false,
  } = params

  return {
    ...defaultMetadata,
    title,
    description,
    icons,
    openGraph: {
      ...defaultOpenGraph,
      title,
      description,
      images: image ? [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: title
        }
      ] : defaultOpenGraph.images,
    },
    twitter: {
      ...defaultTwitter,
      title,
      description,
      images: image ? [image] : defaultTwitter.images,
    },
    robots: noIndex ? {
      index: false,
      follow: true,
      googleBot: {
        index: false,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    } : defaultMetadata.robots,
  }
}