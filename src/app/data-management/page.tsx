"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth-context';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, ChevronLeft, Download, Trash2 } from 'lucide-react';
import Link from 'next/link';

export default function DataManagementPage() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <main className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/settings">
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back to Settings
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Data Management</h1>
          <p className="text-muted-foreground mt-1">
            Export your data or manage your account
          </p>
        </div>
      </div>

      {/* Data Export Section */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Data Export
          </CardTitle>
          <CardDescription>
            Request a complete export of your personal data including all flashcard sets,
            account settings, usage history, and subscription information.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-muted/50 p-4 rounded-lg">
            <p className="text-sm text-muted-foreground mb-3">
              To request a data export, please send an email to{' '}
              <a
                href="mailto:<EMAIL>?subject=Data Export Request&body=Please export all my personal data from Flash Cards AI.%0A%0AAccount email: [Your email address]%0ARequest date: [Today's date]"
                className="text-primary hover:underline font-medium"
              >
                <EMAIL>
              </a>{' '}
              with the subject "Data Export Request".
            </p>
            <div className="text-xs text-muted-foreground">
              <p className="font-medium mb-2">Your export will include:</p>
              <ul className="list-disc list-inside space-y-1">
                <li>All flashcard sets and content (JSON format)</li>
                <li>Account settings and preferences</li>
                <li>Usage history and statistics</li>
                <li>Subscription and billing information</li>
                <li>Any stored API keys (encrypted)</li>
              </ul>
              <p className="mt-3">
                <strong>Timeline:</strong> Data exports are typically delivered within 30 days via secure email.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Account Deletion Section */}
      <Card className="border-destructive/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <Trash2 className="h-5 w-5" />
            Account Deletion
          </CardTitle>
          <CardDescription>
            Permanently delete your account and all associated data. This action cannot be undone.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-destructive/10 border border-destructive/20 p-4 rounded-lg">
            <p className="text-sm text-muted-foreground mb-3">
              To delete your account, please send an email to{' '}
              <a
                href="mailto:<EMAIL>?subject=Account Deletion Request&body=Please delete my Flash Cards AI account and all associated data.%0A%0AAccount email: [Your email address]%0ARequest date: [Today's date]%0A%0AI understand this action is permanent and cannot be undone."
                className="text-primary hover:underline font-medium"
              >
                <EMAIL>
              </a>{' '}
              with the subject "Account Deletion Request".
            </p>
            <div className="text-xs text-muted-foreground">
              <p className="font-medium mb-2 text-destructive">This will permanently delete:</p>
              <ul className="list-disc list-inside space-y-1">
                <li>All your flashcard sets and study history</li>
                <li>Your account settings and preferences</li>
                <li>Any stored API keys (encrypted)</li>
                <li>Usage tracking and billing data</li>
                <li>Subscription information</li>
              </ul>
              <p className="mt-3">
                <strong>Timeline:</strong> Account deletion is typically completed within 30 days.
                We may retain some data for legal or security purposes as outlined in our privacy policy.
              </p>
              <p className="mt-2 text-amber-600">
                <strong>Recommendation:</strong> Consider requesting a data export before deletion
                if you want to keep a copy of your flashcards.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Legal Information */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Legal Information</CardTitle>
          <CardDescription>
            Privacy policy and terms of service
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button variant="outline" asChild className="flex-1">
              <Link href="/legal/privacy">
                Privacy Policy
              </Link>
            </Button>
            <Button variant="outline" asChild className="flex-1">
              <Link href="/legal/terms">
                Terms & Conditions
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </main>
  );
}
