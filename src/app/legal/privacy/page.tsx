import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { ArrowLeft, ExternalLink } from 'lucide-react'

export default function PrivacyPolicyPage() {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <Link href="/">
          <Button variant="ghost" className="mb-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to App
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">Privacy Policy</h1>
        <p className="text-muted-foreground">
          Last Updated: {new Date().toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          })}
        </p>
      </div>

      {/* Notice */}
      <div className="bg-card p-6 rounded-lg border">
        <h2 className="text-xl font-semibold mb-3">Important Notice</h2>
        <p className="text-muted-foreground mb-4">
          This Flash Cards AI application is developed and operated by Anker Studios. 
          Our privacy practices are governed by the comprehensive Anker Studios Privacy Policy, 
          with additional provisions specific to this application outlined below.
        </p>
        <Link 
          href="https://www.ankerstudios.co.za/legal-billing/privacy" 
          target="_blank" 
          rel="noopener noreferrer"
          className="inline-flex items-center text-primary hover:underline"
        >
          View Full Anker Studios Privacy Policy
          <ExternalLink className="w-4 h-4 ml-1" />
        </Link>
      </div>

      {/* Application-Specific Provisions */}
      <div className="space-y-6">
        <section>
          <h2 className="text-2xl font-semibold mb-4">Cookie Usage and Data Collection</h2>

          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2">Cookies and Local Storage</h3>
              <p className="text-muted-foreground mb-2">
                We use cookies and browser local storage to enhance your experience and provide essential functionality:
              </p>
              <ul className="list-disc list-inside text-muted-foreground space-y-1 mb-2">
                <li><strong>Essential cookies:</strong> Required for app functionality including user authentication, session management, and security</li>
                <li><strong>Functional cookies:</strong> Remember your preferences such as sidebar state and user interface settings</li>
                <li><strong>Analytics cookies:</strong> Help us understand how you use our service through Google Analytics (optional, can be disabled)</li>
              </ul>
              <p className="text-muted-foreground">
                You can control your cookie preferences through our consent banner or in your account settings.
                Essential cookies cannot be disabled as they are necessary for the application to function properly.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Analytics and Usage Tracking</h3>
              <p className="text-muted-foreground mb-2">
                When you consent to analytics cookies, we collect:
              </p>
              <ul className="list-disc list-inside text-muted-foreground space-y-1 mb-2">
                <li><strong>Page views and navigation:</strong> Which pages you visit and how you navigate through the app</li>
                <li><strong>Feature usage:</strong> Which features you use and how often</li>
                <li><strong>Performance data:</strong> Page load times and technical performance metrics</li>
                <li><strong>Device information:</strong> Browser type, screen size, and operating system (anonymized)</li>
              </ul>
              <p className="text-muted-foreground">
                This data is processed by Google Analytics and Vercel Analytics. No personal information
                is included in analytics data, and you can opt out at any time through your settings.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Local Storage Usage</h3>
              <p className="text-muted-foreground mb-2">
                We store the following information in your browser's local storage:
              </p>
              <ul className="list-disc list-inside text-muted-foreground space-y-1 mb-2">
                <li><strong>Cookie consent preferences:</strong> Your choices regarding analytics and functional cookies</li>
                <li><strong>Subscription information:</strong> Temporary data during payment processing</li>
                <li><strong>User interface preferences:</strong> Settings that improve your user experience</li>
              </ul>
              <p className="text-muted-foreground">
                Local storage data remains on your device and is not transmitted to our servers unless explicitly needed for functionality.
              </p>
            </div>
          </div>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-4">Application-Specific Data Processing</h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2">Document Processing and File Handling</h3>
              <p className="text-muted-foreground mb-2">
                When you upload documents to generate flashcards, we process them as follows:
              </p>
              <ul className="list-disc list-inside text-muted-foreground space-y-1 mb-2">
                <li><strong>Supported formats:</strong> PDF, Microsoft Word (.docx/.doc), PowerPoint (.pptx/.ppt), plain text (.txt), and Markdown (.md) files</li>
                <li><strong>File size limit:</strong> Maximum 15MB per file</li>
                <li><strong>Temporary storage:</strong> Files are temporarily uploaded to Vercel Blob storage for processing</li>
                <li><strong>AI processing:</strong> Content is extracted and processed using Google Gemini AI to generate flashcards</li>
                <li><strong>Immediate deletion:</strong> All uploaded files are automatically deleted from our servers immediately after processing completion</li>
              </ul>
              <p className="text-muted-foreground">
                We do not retain copies of your uploaded documents beyond the processing session.
                Only the generated flashcard content (questions and answers) is stored in your account.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">AI Service Integration</h3>
              <p className="text-muted-foreground">
                We use Google Gemini AI (specifically Gemini 2.0 Flash model) for flashcard generation and
                ElevenLabs (Flash v2.5 model) for text-to-speech functionality. Your content may be processed
                by these third-party AI services in accordance with their respective privacy policies and our
                data processing agreements.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Text-to-Speech and API Key Storage</h3>
              <p className="text-muted-foreground mb-2">
                Our text-to-speech functionality operates differently based on your subscription tier:
              </p>
              <ul className="list-disc list-inside text-muted-foreground space-y-1 mb-2">
                <li><strong>PRO users:</strong> We process your flashcard text using our ElevenLabs API integration</li>
                <li><strong>FREE/BASIC users:</strong> You may provide your own ElevenLabs API key, which we encrypt using AES-256-CBC encryption and store securely in Firebase Firestore</li>
              </ul>
              <p className="text-muted-foreground">
                User-provided API keys are never stored in plain text and are only decrypted server-side when needed for TTS generation.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Audio Caching and Performance</h3>
              <p className="text-muted-foreground">
                Generated audio content is cached in Vercel Blob storage using deterministic cache keys based on
                text content and voice settings. This caching improves performance and reduces API costs.
                Cached audio files are automatically cleaned up after 30 days. We do not record or store
                your voice or any audio input from your device.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Export Functionality</h3>
              <p className="text-muted-foreground">
                When you export flashcards as PDF or JSON files, this processing occurs on our servers using
                Puppeteer/Chromium for PDF generation. The exported files are generated on-demand and are not
                permanently stored. JSON exports are generated client-side in your browser.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Third-Party Service Integrations</h3>
              <p className="text-muted-foreground mb-2">
                We integrate with the following third-party services to provide our functionality:
              </p>
              <ul className="list-disc list-inside text-muted-foreground space-y-1">
                <li><strong>Google Gemini AI:</strong> Document processing and flashcard generation</li>
                <li><strong>ElevenLabs:</strong> Text-to-speech audio generation</li>
                <li><strong>Firebase (Google):</strong> User authentication, database storage, and user management</li>
                <li><strong>Vercel Blob:</strong> Temporary file storage and audio caching</li>
                <li><strong>Paystack:</strong> Payment processing for subscriptions</li>
                <li><strong>Google Analytics:</strong> Website usage analytics (when enabled)</li>
              </ul>
              <p className="text-muted-foreground mt-2">
                Data shared with these services is processed in accordance with their respective privacy policies
                and our data processing agreements.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">International Data Transfers</h3>
              <p className="text-muted-foreground mb-2">
                Some of our third-party service providers are located outside South Africa. When we transfer your data internationally, we ensure appropriate safeguards are in place:
              </p>
              <ul className="list-disc list-inside text-muted-foreground space-y-1 mb-2">
                <li><strong>Google Services (Gemini AI, Analytics, Firebase):</strong> Data may be processed in the United States and other countries where Google operates, protected by Google's privacy commitments and Standard Contractual Clauses (SCCs)</li>
                <li><strong>ElevenLabs:</strong> Text-to-speech processing may occur in the United States, governed by ElevenLabs' privacy policy and data protection agreements</li>
                <li><strong>Vercel (Hosting & Storage):</strong> Data is primarily processed in the United States with appropriate technical and organizational measures</li>
                <li><strong>Paystack:</strong> Payment processing occurs within Africa and complies with applicable data protection regulations</li>
              </ul>
              <p className="text-muted-foreground">
                All international transfers are conducted in compliance with applicable data protection laws, including the Protection of Personal Information Act (POPIA) and GDPR where applicable. We ensure that adequate safeguards are in place through Standard Contractual Clauses, adequacy decisions, or other approved transfer mechanisms.
              </p>
            </div>
          </div>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-4">Data Retention</h2>
          <div className="space-y-3">
            <p className="text-muted-foreground">
              Your flashcard sets and study history are retained in your account for as long as you maintain
              an active account. You can delete individual flashcard sets or your entire account at any time
              through the application interface.
            </p>
            <div className="text-muted-foreground">
              <p className="font-medium mb-2">Specific retention periods:</p>
              <ul className="list-disc list-inside space-y-1">
                <li><strong>Uploaded files:</strong> Deleted immediately after processing (within minutes)</li>
                <li><strong>Generated flashcards:</strong> Retained until manually deleted by user or account closure</li>
                <li><strong>Usage tracking data:</strong> Retained for billing and rate limiting purposes</li>
                <li><strong>Cached audio files:</strong> Automatically deleted after 30 days</li>
                <li><strong>User settings and API keys:</strong> Retained until account deletion</li>
              </ul>
            </div>
          </div>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-4">Your Rights</h2>
          <div className="space-y-4">
            <p className="text-muted-foreground">
              Under applicable data protection laws (including POPIA and GDPR), you have the following rights regarding your personal data:
            </p>

            <div className="space-y-3">
              <div>
                <h3 className="text-lg font-medium mb-2">Right to Access</h3>
                <p className="text-muted-foreground">
                  You can request a copy of all personal data we hold about you. This includes your flashcard sets,
                  account information, usage data, and settings. Contact <NAME_EMAIL> to request your data export.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Right to Rectification</h3>
                <p className="text-muted-foreground">
                  You can update your personal information through your account settings or by contacting us if you
                  believe any information we hold is inaccurate or incomplete.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Right to Erasure (Right to be Forgotten)</h3>
                <p className="text-muted-foreground">
                  You can request deletion of your account and all associated data. This includes all flashcard sets,
                  usage history, settings, and any stored API keys. See your account settings for deletion instructions
                  or contact us directly.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Right to Data Portability</h3>
                <p className="text-muted-foreground">
                  You can request your data in a structured, commonly used format (JSON/CSV) that allows you to
                  transfer it to another service. Contact us to request a data export.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Right to Object and Withdraw Consent</h3>
                <p className="text-muted-foreground">
                  You can withdraw consent for analytics cookies and data processing at any time through your
                  account settings. You can also object to processing based on legitimate interests.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Response Timeframes</h3>
                <p className="text-muted-foreground">
                  We will respond to your requests within 30 days. For complex requests, we may extend this
                  period by up to 60 days and will inform you of any delays.
                </p>
              </div>
            </div>
          </div>
        </section>
      </div>

      {/* Contact Information */}
      <div className="bg-muted p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Contact Us</h2>
        <p className="text-muted-foreground mb-2">
          For privacy-related questions or to exercise your rights, please contact:
        </p>
        <div className="space-y-1 text-sm">
          <p><strong>Anker Studios</strong></p>
          <p>Email: <EMAIL></p>
          <p>Website: www.ankerstudios.co.za</p>
        </div>
      </div>
    </div>
  )
}
