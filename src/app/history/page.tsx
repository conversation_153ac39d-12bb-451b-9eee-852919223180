"use client";

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { useFlashcardService, FlashcardSet } from '@/lib/flashcard-service';
import { useAuth } from '@/lib/auth-context';
import { Button } from '@/components/ui/button';
import { ExportButton } from '@/components/ExportButton';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { format } from 'date-fns';
import { Clock, FileText, LogOut, User, ArrowLeft, Loader2, Trash2, AlertTriangle, Settings } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { logger } from '@/lib/logger';

export default function History() {
  const [flashcardSets, setFlashcardSets] = useState<FlashcardSet[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeletingId, setIsDeletingId] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [setToDelete, setSetToDelete] = useState<FlashcardSet | null>(null);

  const { getUserSets, deleteUserFlashcardSet } = useFlashcardService();
  const { user, loading, logout } = useAuth();
  const router = useRouter();
  const { toast } = useToast();

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!loading && !user) {
      router.push('/auth/signin?redirect=/history');
    }
  }, [user, loading, router]);

  const fetchFlashcardSets = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const sets = await getUserSets();
      setFlashcardSets(sets);
    } catch (error) {
      logger.error('Error fetching flashcard sets:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load your flashcard history",
      });
    } finally {
      setIsLoading(false);
    }
  }, [user, getUserSets, toast]);

  useEffect(() => {
    if (user) {
      fetchFlashcardSets();
    }
  }, [user, fetchFlashcardSets]);

  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: "Logged out",
        description: "You have been successfully logged out.",
      });
      router.push('/');
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to log out",
      });
    }
  };

  const handleViewSet = (setId: string) => {
    router.push(`/history/${setId}`);
  };

  const handleDeleteClick = (set: FlashcardSet) => {
    setSetToDelete(set);
    setShowDeleteConfirm(true);
  };

  const handleDeleteConfirm = async () => {
    if (!setToDelete) return;

    setIsDeletingId(setToDelete.id);
    try {
      const result = await deleteUserFlashcardSet(setToDelete.id);
      if (result.success) {
        // Remove the deleted set from the state
        setFlashcardSets(prev => prev.filter(set => set.id !== setToDelete.id));
        toast({
          title: "Deleted",
          description: "Flashcard set has been deleted",
        });
      } else {
        throw new Error(result.error as string || 'Failed to delete flashcard set');
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete flashcard set",
      });
    } finally {
      setIsDeletingId(null);
      setShowDeleteConfirm(false);
      setSetToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(false);
    setSetToDelete(null);
  };

  if (loading || (user && isLoading)) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4 pwa-safe-all">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="mt-4 text-lg">Loading your flashcard history...</p>
      </div>
    );
  }

  return (
    <main className="flex flex-col min-h-screen p-4 sm:p-8 bg-background pwa-safe-all">
      <header className="mb-8 w-full max-w-4xl mx-auto">
        {/* User navigation */}
        <div className="flex justify-end mb-4">
          {user && (
            <div className="flex items-center gap-2">
              {process.env.NEXT_PUBLIC_ENABLE_SUBSCRIPTIONS === 'true' && (
                <Link href="/subscription">
                  <Button variant="outline" size="sm" className="mr-2">
                    <span>Subscription</span>
                  </Button>
                </Link>
              )}
              <Link href="/settings">
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2"
                >
                  <Settings className="h-4 w-4" />
                  <span className="hidden sm:inline">Settings</span>
                </Button>
              </Link>
              <div className="flex items-center gap-2 text-sm bg-muted p-2 rounded-lg">
                <User className="h-4 w-4" />
                <span className="hidden sm:inline">{user.name || user.email}</span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleLogout}
                aria-label="Logout"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        {/* Navigation and title */}
        <div className="flex flex-col">
          <div className="flex items-center mb-2">
            <Link href="/" className="flex items-center">
              <Button variant="ghost" size="icon" className="mr-2">
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <Image
                src="/flashcard_icon.png"
                alt="Flashcard AI Logo"
                width={40}
                height={40}
              />
              <span className="ml-2 text-2xl font-bold text-foreground">Flashcard History</span>
            </Link>
          </div>
          <p className="text-muted-foreground">
            View your previously generated flashcard sets
          </p>
        </div>
      </header>

      <div className="w-full max-w-4xl mx-auto">
        {flashcardSets.length === 0 ? (
          <div className="text-center p-12 bg-card rounded-lg shadow-md">
            <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h2 className="text-xl font-semibold mb-2 text-foreground">No Flashcard Sets Found</h2>
            <p className="text-muted-foreground mb-6">
              You haven't created any flashcard sets yet. Generate some flashcards to see them here.
            </p>
            <Button asChild>
              <Link href="/">Generate Flashcards</Link>
            </Button>
          </div>
        ) : (
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {flashcardSets.map((set) => (
              <Card key={set.id} className="overflow-hidden">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg truncate">{set.title}</CardTitle>
                  <CardDescription className="flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {format(set.createdAt, 'PPP')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    {set.flashcards.length} flashcards
                  </p>
                </CardContent>
                <CardFooter className="flex flex-col gap-2">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => handleViewSet(set.id)}
                  >
                    View Flashcards
                  </Button>
                  <div className="flex gap-2 w-full">
                    <div className="flex-1">
                      <ExportButton flashcards={set.flashcards} title={set.title} />
                    </div>
                    <Button
                      variant="outline"
                      size="icon"
                      className="text-destructive hover:text-destructive hover:bg-destructive/10 flex-shrink-0"
                      onClick={() => handleDeleteClick(set)}
                      disabled={isDeletingId === set.id}
                      aria-label="Delete flashcard set"
                    >
                      {isDeletingId === set.id ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Trash2 className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Delete Flashcard Set
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete &quot;{setToDelete?.title}&quot;? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleDeleteCancel}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeletingId ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Trash2 className="h-4 w-4 mr-2" />
              )}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </main>
  );
}