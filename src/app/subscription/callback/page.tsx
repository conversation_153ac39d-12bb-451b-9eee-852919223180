"use client";

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { usePaystackService } from '@/lib/paystack-service';
import { useUsageService } from '@/lib/usage-service';
import { useAuth } from '@/lib/auth-context';
import { useSubscription } from '@/hooks/use-subscription';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { logger } from '@/lib/logger';

function PaymentCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const { user, loading: authLoading } = useAuth();
  const { verifyTransaction } = usePaystackService();
  const { updateTier } = useUsageService();
  const { refreshSubscription, tier: currentTier } = useSubscription();
  
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Verifying your payment...');
  const [tier, setTier] = useState<string | null>(null);
  const [verificationComplete, setVerificationComplete] = useState(false);
  const [isSuccessfulPayment, setIsSuccessfulPayment] = useState(false);

  // First useEffect to verify the payment
  useEffect(() => {
    const verifyPayment = async () => {
      // Get the transaction reference from the URL
      const reference = searchParams.get('reference');
      
      if (!reference) {
        setStatus('error');
        setMessage('Invalid payment reference. Please try again or contact support.');
        return;
      }

      try {
        // Get the intended tier from localStorage (set during upgrade process)
        const intendedTier = localStorage.getItem('pendingSubscriptionTier') || null;
        logger.log('Intended tier from localStorage:', intendedTier);
        
        // Verify the transaction with Paystack
        const isSuccessful = await verifyTransaction(reference);
        
        if (isSuccessful) {
          setIsSuccessfulPayment(true);
          
          // Don't update tier here - we'll do it in the second useEffect when auth is ready
          // Just store the verification result
          
          setStatus('success');
          setMessage('Your payment was successful! Your subscription is being activated...');
          
          // Set verification as complete and successful
          setVerificationComplete(true);
        } else {
          setStatus('error');
          setMessage('Payment verification failed. Please try again or contact support.');
          
          toast({
            variant: 'destructive',
            title: 'Payment Failed',
            description: 'We could not verify your payment. Please try again or contact support.',
          });
        }
      } catch (error) {
        logger.error('Error verifying payment:', error);
        setStatus('error');
        setMessage('An error occurred while verifying your payment. Please try again or contact support.');
        
        toast({
          variant: 'destructive',
          title: 'Verification Error',
          description: 'An error occurred while verifying your payment.',
        });
      }
    };

    verifyPayment();
  }, [searchParams, verifyTransaction, toast]);

  // Second useEffect to update the tier once auth is ready and payment is verified
  useEffect(() => {
    const updateUserTier = async () => {
      // Only proceed if payment verification was successful and auth is ready
      if (!verificationComplete || !isSuccessfulPayment || authLoading || !user) {
        return;
      }
      
      try {
        // Get the intended tier from localStorage
        const intendedTier = localStorage.getItem('pendingSubscriptionTier') || null;
        
        if (intendedTier && ['BASIC', 'PRO'].includes(intendedTier)) {
          logger.log(`Manually updating tier to ${intendedTier}`);
          await updateTier(intendedTier as any);
          
          // Clear the pending tier from localStorage
          localStorage.removeItem('pendingSubscriptionTier');

          // Refresh subscription data in the context
          await refreshSubscription();

          setTier(intendedTier);
          setMessage('Your payment was successful! Your subscription has been activated.');

          toast({
            title: 'Subscription Activated',
            description: `Your ${intendedTier} subscription has been activated successfully.`,
          });
        }
      } catch (error) {
        logger.error('Error updating subscription tier:', error);
        // Don't change status to error since payment was successful
        // Just show a warning about the tier update
        toast({
          variant: 'destructive',
          title: 'Tier Update Warning',
          description: 'Payment successful, but there was an issue updating your subscription tier. Please refresh the page.',
        });
      }
    };

    updateUserTier();
  }, [user, authLoading, verificationComplete, isSuccessfulPayment, updateTier, refreshSubscription, toast]);

  return (
      <div className="container py-16 px-4 max-w-lg mx-auto">
        <div className="bg-card p-8 rounded-xl shadow-lg">
          <div className="flex flex-col items-center text-center">
            {status === 'loading' && (
              <Loader2 className="h-16 w-16 text-primary animate-spin mb-4" />
            )}
            
            {status === 'success' && (
              <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
            )}
            
            {status === 'error' && (
              <XCircle className="h-16 w-16 text-destructive mb-4" />
            )}
            
            <h1 className="text-2xl font-bold mb-2">
              {status === 'loading' && 'Processing Payment'}
              {status === 'success' && 'Payment Successful'}
              {status === 'error' && 'Payment Failed'}
            </h1>
            
            <p className="text-muted-foreground mb-8">{message}</p>
            
            {status === 'success' && tier && (
              <div className="bg-primary/10 p-4 rounded-lg mb-6">
                <p className="font-medium">Your {tier} plan is now active!</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Enjoy the benefits of your new subscription.
                </p>
              </div>
            )}
            
            <div className="flex gap-4">
              <Button onClick={() => router.push('/subscription')}>
                Back to Subscriptions
              </Button>
              
              <Button variant="outline" onClick={() => router.push('/')}>
                Go to Home
              </Button>
            </div>
          </div>
        </div>
      </div>
  );
}

export default function PaymentCallback() {
  return (
    <Suspense fallback={<div>Loading page...</div>}>
      <PaymentCallbackContent />
    </Suspense>
  );
} 