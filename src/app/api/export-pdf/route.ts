import { NextRequest, NextResponse } from 'next/server';
import puppeteer from 'puppeteer-core';
import chromium from '@sparticuz/chromium';
import { logger } from '@/lib/logger';
import type { FlashcardData } from '@/types';

// Calculate how many cards fit per page (2 columns, estimated rows based on A4 size)
const CARDS_PER_PAGE = 8; // 2 columns × 4 rows fits well on A4 with margins

// Generate HTML for a single page of flashcards
function generateFlashcardPageHTML(flashcards: FlashcardData[], title: string, type: 'questions' | 'answers', pageNumber: number, totalPages: number, startIndex: number): string {
  // For answer pages, reverse column positions within each row (2 cards per row)
  // This ensures proper alignment when the page is horizontally mirrored and printed double-sided
  let orderedFlashcards = flashcards;
  if (type === 'answers') {
    orderedFlashcards = [];
    for (let i = 0; i < flashcards.length; i += 2) {
      // For each row (2 cards), reverse the order: [card1, card2] → [card2, card1]
      const card1 = flashcards[i];
      const card2 = flashcards[i + 1];
      if (card2) {
        orderedFlashcards.push(card2, card1); // Swap columns within the row
      } else {
        orderedFlashcards.push(card1); // Odd number of cards, keep the last one as-is
      }
    }
  }

  const cards = orderedFlashcards.map((card, index) => {
    const content = type === 'questions' ? card.question : card.answer;
    const label = type === 'questions' ? 'QUESTION' : 'ANSWER';

    // For answer pages, find the original card number by looking up the card in the original array
    let cardNumber;
    if (type === 'answers') {
      const originalIndex = flashcards.findIndex(originalCard => originalCard === card);
      cardNumber = startIndex + originalIndex + 1;
    } else {
      cardNumber = startIndex + index + 1;
    }

    return `
      <div class="flashcard">
        <div class="card-header">
          <span class="card-label">${label}</span>
          <span class="card-number">${cardNumber}</span>
        </div>
        <div class="card-content">
          ${content}
        </div>
      </div>
    `;
  }).join('');

  const isMirrored = type === 'answers';

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${title} - ${type === 'questions' ? 'Questions' : 'Answers'} Page ${pageNumber}</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: #fafafa;
          color: #1a1a1a;
          line-height: 1.5;
          padding: 20px;
          /* Horizontally mirror answer pages for double-sided printing alignment */
          ${isMirrored ? 'transform: scaleX(-1);' : ''}
        }

        .page-title {
          text-align: center;
          margin-bottom: 20px;
          padding-bottom: 15px;
          border-bottom: 1px solid #e5e5e5;
        }

        .page-title h1 {
          font-size: 22px;
          font-weight: 700;
          color: #1a1a1a;
          margin-bottom: 6px;
        }

        .page-title .subtitle {
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }

        .flashcard-grid {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          max-width: 100%;
          margin: 0 auto;
          width: 100%;
          justify-content: space-between;
        }

        .flashcard {
          background: white;
          border-radius: 8px;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
          border: 1px solid #e5e5e5;
          padding: 16px;
          height: 160px;
          width: calc(50% - 7.5px);
          display: flex;
          flex-direction: column;
          break-inside: avoid;
          page-break-inside: avoid;
          overflow: hidden;
          box-sizing: border-box;
          flex-shrink: 0;
          /* Double mirror: body is mirrored, then individual cards are mirrored back to readable orientation */
          ${isMirrored ? 'transform: scaleX(-1);' : ''}
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px solid #f0f0f0;
          flex-shrink: 0;
        }

        .card-label {
          font-size: 10px;
          font-weight: 600;
          color: #666;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .card-number {
          font-size: 9px;
          color: #999;
          font-weight: 500;
        }

        .card-content {
          flex: 1;
          font-size: 13px;
          line-height: 1.4;
          color: #1a1a1a;
          text-align: left;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 6;
          -webkit-box-orient: vertical;
        }

        /* Print styles */
        @media print {
          body {
            background: white;
            padding: 10px;
            ${isMirrored ? 'transform: scaleX(-1);' : ''}
          }

          .flashcard-grid {
            display: flex !important;
            flex-wrap: wrap !important;
            gap: 12px !important;
            width: 100% !important;
            justify-content: space-between !important;
          }

          .flashcard {
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            border: 1px solid #ddd;
            height: 140px !important;
            width: calc(50% - 6px) !important;
            display: flex !important;
            flex-direction: column !important;
            flex-shrink: 0 !important;
            ${isMirrored ? 'transform: scaleX(-1);' : ''}
          }

          .page-title {
            margin-bottom: 20px;
          }

          .card-content {
            font-size: 12px;
            line-height: 1.3;
            -webkit-line-clamp: 5;
          }
        }

        /* Page break handling */
        .page-break {
          page-break-before: always;
          clear: both;
        }
      </style>
    </head>
    <body>
      <div class="page-title">
        <h1>${title}</h1>
        <div class="subtitle">${type === 'questions' ? 'Questions' : 'Answers'} • Page ${pageNumber} of ${totalPages}</div>
      </div>

      <div class="flashcard-grid">
        ${cards}
      </div>
    </body>
    </html>
  `;
}

// Generate zipper-style HTML with alternating question/answer pages
function generateZipperHTML(flashcards: FlashcardData[], title: string): string {
  const totalCards = flashcards.length;
  const totalQuestionPages = Math.ceil(totalCards / CARDS_PER_PAGE);
  const totalPages = totalQuestionPages * 2; // Each question page has a corresponding answer page

  let combinedHTML = '';

  for (let pageIndex = 0; pageIndex < totalQuestionPages; pageIndex++) {
    const startIndex = pageIndex * CARDS_PER_PAGE;
    const endIndex = Math.min(startIndex + CARDS_PER_PAGE, totalCards);
    const pageFlashcards = flashcards.slice(startIndex, endIndex);

    // Question page
    const questionPageNumber = (pageIndex * 2) + 1;
    const questionHTML = generateFlashcardPageHTML(
      pageFlashcards,
      title,
      'questions',
      questionPageNumber,
      totalPages,
      startIndex
    );

    // Answer page (mirrored)
    const answerPageNumber = (pageIndex * 2) + 2;
    const answerHTML = generateFlashcardPageHTML(
      pageFlashcards,
      title,
      'answers',
      answerPageNumber,
      totalPages,
      startIndex
    );

    if (pageIndex === 0) {
      // First question page - no page break needed
      combinedHTML += questionHTML.replace('</body></html>', '');
    } else {
      // Subsequent question pages - add page break
      combinedHTML += `<div class="page-break"></div>`;
      combinedHTML += questionHTML.replace(/<!DOCTYPE html>[\s\S]*?<body[^>]*>/, '').replace('</body></html>', '');
    }

    // Add page break before answer page
    combinedHTML += `<div class="page-break"></div>`;
    combinedHTML += answerHTML.replace(/<!DOCTYPE html>[\s\S]*?<body[^>]*>/, '').replace('</body></html>', '');
  }

  // Close the HTML
  combinedHTML += '</body></html>';

  return combinedHTML;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { flashcards, title }: { flashcards: FlashcardData[], title?: string } = body;

    if (!flashcards || !Array.isArray(flashcards) || flashcards.length === 0) {
      return NextResponse.json(
        { error: 'Invalid flashcards data' },
        { status: 400 }
      );
    }

    const flashcardTitle = title || 'Flashcards';

    // Launch Puppeteer with @sparticuz/chromium for Vercel compatibility
    const isProduction = process.env.NODE_ENV === 'production';

    logger.log('Environment:', isProduction ? 'production' : 'development');

    let browser;

    if (isProduction) {
      // Use @sparticuz/chromium for production/Vercel
      browser = await puppeteer.launch({
        args: chromium.args,
        defaultViewport: chromium.defaultViewport,
        executablePath: await chromium.executablePath(),
        headless: chromium.headless,
        ignoreHTTPSErrors: true,
      });
    } else {
      // Use local Puppeteer for development
      const localPuppeteer = await import('puppeteer');
      browser = await localPuppeteer.default.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu'
        ]
      });
    }

    try {
      const page = await browser.newPage();

      // Generate zipper-style HTML with alternating question/answer pages
      const combinedHTML = generateZipperHTML(flashcards, flashcardTitle);

      // Set content
      await page.setContent(combinedHTML, {
        waitUntil: 'networkidle0',
        timeout: 30000
      });

      // Generate PDF with print-optimized settings
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0.4in',
          right: '0.4in',
          bottom: '0.4in',
          left: '0.4in'
        },
        preferCSSPageSize: true,
        scale: 0.9, // Slightly reduce scale to fit more content
      });

      // Create filename
      const filename = title
        ? `${title.replace(/[^a-zA-Z0-9]/g, '_')}_flashcards.pdf`
        : 'flashcards.pdf';

      // Return the PDF
      return new NextResponse(pdfBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Length': pdfBuffer.length.toString(),
        },
      });

    } finally {
      await browser.close();
    }

  } catch (error) {
    logger.error('Error generating PDF:', error);

    // Provide more specific error information
    let errorMessage = 'Failed to generate PDF';
    if (error instanceof Error) {
      if (error.message.includes('Chrome')) {
        errorMessage = 'Chrome browser not available. Please try again later.';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'PDF generation timed out. Please try with fewer flashcards.';
      } else {
        errorMessage = `PDF generation failed: ${error.message}`;
      }
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
