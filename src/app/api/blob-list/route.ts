import { NextRequest, NextResponse } from 'next/server';
import { list } from '@vercel/blob';
import { logger } from '@/lib/logger';

interface BlobListRequest {
  prefix?: string;
  limit?: number;
}

interface BlobListResponse {
  success: boolean;
  blobs?: any[];
  error?: string;
}

export async function POST(request: NextRequest): Promise<NextResponse<BlobListResponse>> {
  try {
    const body: BlobListRequest = await request.json();
    const { prefix, limit = 100 } = body;

    logger.log('[Blob List API] Listing blobs with prefix:', prefix);

    // List blobs with optional prefix filter
    const { blobs } = await list({
      prefix,
      limit
    });

    logger.log('[Blob List API] Found', blobs.length, 'blobs');

    return NextResponse.json({
      success: true,
      blobs
    });

  } catch (error) {
    logger.error('[Blob List API] Error listing blobs:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to list blobs'
      },
      { status: 500 }
    );
  }
}
