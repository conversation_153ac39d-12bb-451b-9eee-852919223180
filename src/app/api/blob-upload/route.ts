import { handleUpload, type <PERSON>leUploadBody } from '@vercel/blob/client';
import { NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

export async function POST(request: Request): Promise<NextResponse> {
  const body = (await request.json()) as HandleUploadBody;

  try {
    const jsonResponse = await handleUpload({
      body,
      request,
      onBeforeGenerateToken: async (pathname: string) => {
        logger.log('Received upload pathname:', pathname);
        // TODO: Add authentication logic here if needed
        // For now, allow all uploads to 'uploads/'
        if (!pathname.startsWith('uploads/')) {
          throw new Error('Invalid upload path');
        }
        return {
          allowedContentTypes: [
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'application/vnd.ms-powerpoint',
            'text/plain',
            'text/markdown'
          ],
          tokenPayload: JSON.stringify({}),
        };
      },
      onUploadCompleted: async ({ blob, tokenPayload }) => {
        // Optionally handle post-upload logic here
        logger.log('File upload completed', blob, tokenPayload);
      },
    });
    return NextResponse.json(jsonResponse);
  } catch (error) {
    return NextResponse.json(
      { error: (error as Error).message },
      { status: 400 },
    );
  }
} 