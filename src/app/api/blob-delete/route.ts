import { del } from '@vercel/blob';
import { NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

export async function POST(request: Request) {
  try {
    const { url } = await request.json();
    
    if (!url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }

    await del(url);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error('Error deleting blob:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete blob' },
      { status: 500 }
    );
  }
} 