import { NextResponse } from 'next/server';
import axios from 'axios';
import { logger } from '@/lib/logger';

// Paystack API base URL
const PAYSTACK_API_URL = 'https://api.paystack.co';

export async function GET(request: Request) {
  try {
    logger.log('[Paystack Debug] Starting subscription debug endpoint');
    
    // Get the secret key from environment variables
    const secretKey = process.env.PAYSTACK_SECRET_KEY;
    if (!secretKey) {
      return NextResponse.json(
        { status: false, message: 'Paystack secret key is not configured' },
        { status: 500 }
      );
    }
    
    try {
      // Get all customers
      logger.log('[Paystack Debug] Fetching all customers');
      const customersResponse = await axios.get(
        `${PAYSTACK_API_URL}/customer`,
        {
          headers: {
            Authorization: `Bearer ${secretKey}`,
            'Content-Type': 'application/json',
          },
        }
      );
      
      logger.log(`[Paystack Debug] Customer count: ${customersResponse.data.data?.length || 0}`);
      
      // Get all subscriptions
      logger.log('[Paystack Debug] Fetching all subscriptions');
      const subscriptionsResponse = await axios.get(
        `${PAYSTACK_API_URL}/subscription`,
        {
          headers: {
            Authorization: `Bearer ${secretKey}`,
            'Content-Type': 'application/json',
          },
        }
      );
      
      const subscriptions = subscriptionsResponse.data.data || [];
      logger.log(`[Paystack Debug] Subscription count: ${subscriptions.length}`);
      
      if (subscriptions.length > 0) {
        logger.log('[Paystack Debug] Subscription details:');
        subscriptions.forEach((sub: any, index: number) => {
          logger.log(`[Paystack Debug] Subscription #${index + 1}:`, {
            code: sub.subscription_code,
            email: sub.customer?.email,
            status: sub.status,
            plan: sub.plan?.name,
            createdAt: sub.createdAt
          });
        });
      }
      
      // Get all transactions
      logger.log('[Paystack Debug] Fetching recent transactions');
      const transactionsResponse = await axios.get(
        `${PAYSTACK_API_URL}/transaction`,
        {
          headers: {
            Authorization: `Bearer ${secretKey}`,
            'Content-Type': 'application/json',
          },
        }
      );
      
      const transactions = transactionsResponse.data.data || [];
      logger.log(`[Paystack Debug] Recent transaction count: ${transactions.length}`);
      
      // Return diagnostic data
      return NextResponse.json({
        status: true,
        diagnostics: {
          customerCount: customersResponse.data.data?.length || 0,
          subscriptionCount: subscriptions.length,
          transactionCount: transactions.length,
          subscriptions: subscriptions.map((sub: any) => ({
            code: sub.subscription_code,
            email: sub.customer?.email,
            status: sub.status,
            plan: sub.plan?.name,
            created: sub.createdAt
          })),
          recentTransactions: transactions.slice(0, 5).map((txn: any) => ({
            reference: txn.reference,
            email: txn.customer?.email,
            amount: txn.amount,
            status: txn.status,
            created: txn.createdAt
          }))
        }
      });
      
    } catch (error: any) {
      logger.error('[Paystack Debug] API error:', error.response?.data || error.message);
      return NextResponse.json(
        { 
          status: false, 
          message: 'Error accessing Paystack API',
          error: error.response?.data || error.message
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    logger.error('[Paystack Debug] Unexpected error:', error);
    return NextResponse.json(
      { status: false, message: 'Unexpected error occurred', error: error.message },
      { status: 500 }
    );
  }
} 