import { NextResponse } from 'next/server';
import axios from 'axios';
import { logger } from '@/lib/logger';

// Paystack API base URL
const PAYSTACK_API_URL = 'https://api.paystack.co';

// Mapping plan codes to amounts in cents (South African Rand - ZAR)
const PLAN_AMOUNTS: Record<string, number> = {
  [process.env.BASIC_PLAN_CODE || '']: 2900, // BASIC plan: R29 (2900 cents)
  [process.env.PRO_PLAN_CODE || '']: 7500, // PRO plan: R75 (7500 cents)
};

export async function POST(request: Request) {
  try {
    // Get the secret key from environment variables
    const secretKey = process.env.PAYSTACK_SECRET_KEY;
    if (!secretKey) {
      logger.error('Paystack secret key is not configured');
      return NextResponse.json(
        { status: false, message: 'Paystack secret key is not configured' },
        { status: 500 }
      );
    }

    // Parse the request body
    const body = await request.json();
    const { email, plan } = body;

    if (!email || !plan) {
      logger.error('Missing required parameters:', { email: !!email, plan: !!plan });
      return NextResponse.json(
        { status: false, message: 'Email and plan are required' },
        { status: 400 }
      );
    }

    // Get amount for the plan (in cents)
    const amount = PLAN_AMOUNTS[plan];
    if (!amount) {
      logger.error(`Unknown plan or missing amount for plan: ${plan}`);
      return NextResponse.json(
        { status: false, message: 'Invalid plan code' },
        { status: 400 }
      );
    }

    logger.log(`Initializing transaction for email: ${email}, plan: ${plan}, amount: ${amount} ZAR cents`);

    // Call the Paystack API to initialize the transaction
    try {
      const callbackBaseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:9002';
      const response = await axios.post(
        `${PAYSTACK_API_URL}/transaction/initialize`,
        {
          email,
          plan,
          amount,
          currency: 'ZAR', // Set currency to South African Rand
          callback_url: `${callbackBaseUrl}/subscription/callback`,
        },
        {
          headers: {
            Authorization: `Bearer ${secretKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      logger.log('Transaction initialized successfully');
      
      // Return the response from Paystack
      return NextResponse.json(response.data);
    } catch (apiError: any) {
      logger.error('Paystack API error:', apiError.response?.data || apiError.message);
      return NextResponse.json(
        {
          status: false,
          message: 'Failed to initialize transaction with Paystack',
          error: apiError.response?.data || apiError.message,
        },
        { status: apiError.response?.status || 500 }
      );
    }
  } catch (error: any) {
    logger.error('Error initializing transaction:', error.message);
    return NextResponse.json(
      {
        status: false,
        message: 'Failed to initialize transaction',
        error: error.message,
      },
      { status: 500 }
    );
  }
} 