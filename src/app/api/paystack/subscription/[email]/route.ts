import { NextResponse } from 'next/server';
import axios from 'axios';
import { logger } from '@/lib/logger';

// Paystack API base URL
const PAYSTACK_API_URL = 'https://api.paystack.co';

export async function GET(
  request: Request,
  { params }: { params: { email: string } }
) {
  try {
    // Get the email from the URL params
    // In Next.js 15, we need to await params before accessing its properties
    const emailParam = await params.email;

    if (!emailParam) {
      return NextResponse.json(
        { status: false, message: 'Customer email is required' },
        { status: 400 }
      );
    }

    logger.log(`[Paystack API] Looking up subscriptions for email: ${emailParam}`);

    // Get the secret key from environment variables
    const secretKey = process.env.PAYSTACK_SECRET_KEY;
    if (!secretKey) {
      logger.error('[Paystack API] Secret key not configured');
      return NextResponse.json(
        { status: false, message: 'Paystack secret key is not configured' },
        { status: 500 }
      );
    }

    try {
      // First, we need to find the customer by email
      logger.log(`[Paystack API] Fetching customer info for email: ${emailParam}`);
      const customerResponse = await axios.get(
        `${PAYSTACK_API_URL}/customer?email=${encodeURIComponent(emailParam)}`,
        {
          headers: {
            Authorization: `Bearer ${secretKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      logger.log(`[Paystack API] Customer lookup response:`, {
        status: customerResponse.data.status,
        hasData: !!customerResponse.data.data,
        dataLength: customerResponse.data.data ? customerResponse.data.data.length : 0
      });

      // If customer is not found, return an empty subscription list instead of an error
      if (!customerResponse.data.status || !customerResponse.data.data || customerResponse.data.data.length === 0) {
        logger.log(`[Paystack API] Customer not found for email: ${emailParam}. Returning empty subscriptions.`);
        return NextResponse.json({ status: true, data: [] });
      }

      const customer = customerResponse.data.data[0];
      logger.log(`[Paystack API] Found customer: ${customer.email}, code: ${customer.customer_code}`);

      // Now, try fetching subscriptions for this customer
      logger.log(`[Paystack API] Fetching subscriptions for customer: ${customer.customer_code}`);
      const subscriptionResponse = await axios.get(
        `${PAYSTACK_API_URL}/subscription?customer=${customer.customer_code}`,
        {
          headers: {
            Authorization: `Bearer ${secretKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      logger.log(`[Paystack API] Subscription lookup response:`, {
        status: subscriptionResponse.data.status,
        hasData: !!subscriptionResponse.data.data,
        dataLength: subscriptionResponse.data.data ? subscriptionResponse.data.data.length : 0
      });
      
      // If no subscriptions found by customer code, try listing all subscriptions and filter by email
      if (!subscriptionResponse.data.data || subscriptionResponse.data.data.length === 0) {
        logger.log(`[Paystack API] No subscriptions found for customer. Trying to list all active subscriptions...`);
        
        // List all subscriptions
        const allSubscriptionsResponse = await axios.get(
          `${PAYSTACK_API_URL}/subscription`,
          {
            headers: {
              Authorization: `Bearer ${secretKey}`,
              'Content-Type': 'application/json',
            },
          }
        );
        
        logger.log(`[Paystack API] All subscriptions response:`, {
          status: allSubscriptionsResponse.data.status,
          hasData: !!allSubscriptionsResponse.data.data,
          dataLength: allSubscriptionsResponse.data.data ? allSubscriptionsResponse.data.data.length : 0
        });
        
        // If we got data back, filter it by email
        if (allSubscriptionsResponse.data.status && 
            allSubscriptionsResponse.data.data && 
            allSubscriptionsResponse.data.data.length > 0) {
          
          // Filter subscriptions by customer email
          const customerSubscriptions = allSubscriptionsResponse.data.data.filter(
            (sub: any) => sub.customer?.email === emailParam
          );
          
          logger.log(`[Paystack API] Found ${customerSubscriptions.length} subscriptions for ${emailParam} in the full list`);
          
          if (customerSubscriptions.length > 0) {
            logger.log(`[Paystack API] Subscription statuses from full list:`, 
              customerSubscriptions.map((sub: any) => ({ 
                code: sub.subscription_code, 
                status: sub.status, 
                planName: sub.plan?.name 
              }))
            );
            
            // Return these subscriptions
            return NextResponse.json({
              status: true,
              data: customerSubscriptions
            });
          }
        }
        
        logger.log(`[Paystack API] No subscriptions found for customer`);
        return NextResponse.json({ status: true, data: [] });
      }
      
      if (subscriptionResponse.data.data && subscriptionResponse.data.data.length > 0) {
        logger.log(`[Paystack API] Subscription statuses:`, 
          subscriptionResponse.data.data.map((sub: any) => ({ 
            code: sub.subscription_code, 
            status: sub.status, 
            planName: sub.plan?.name 
          }))
        );
      } else {
        logger.log(`[Paystack API] No subscriptions found for customer`);
      }

      // Return the subscriptions
      return NextResponse.json(subscriptionResponse.data);
    } catch (error: any) {
      logger.error('[Paystack API] Error:', error.response?.data || error.message);
      
      // If it's a 404 Not Found from Paystack, return empty subscriptions instead of error
      if (error.response?.status === 404) {
        logger.log('[Paystack API] 404 error from Paystack API');
        return NextResponse.json({ status: true, data: [] });
      }
      
      throw error; // Re-throw to be caught by the outer catch
    }
  } catch (error: any) {
    logger.error('[Paystack API] Uncaught error:', error.response?.data || error.message);
    return NextResponse.json(
      {
        status: false,
        message: 'Failed to fetch subscription',
        error: error.response?.data || error.message,
      },
      { status: 500 }
    );
  }
} 