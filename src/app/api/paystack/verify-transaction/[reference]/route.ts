import { NextResponse } from 'next/server';
import axios from 'axios';
import { logger } from '@/lib/logger';

// Paystack API base URL
const PAYSTACK_API_URL = 'https://api.paystack.co';

export async function GET(
  request: Request,
  { params }: { params: { reference: string } }
) {
  try {
    // Get the reference from the URL params
    // In Next.js 15, we need to await params before accessing its properties
    const reference = await params.reference;

    if (!reference) {
      return NextResponse.json(
        { status: false, message: 'Transaction reference is required' },
        { status: 400 }
      );
    }

    // Get the secret key from environment variables
    const secretKey = process.env.PAYSTACK_SECRET_KEY;
    if (!secretKey) {
      return NextResponse.json(
        { status: false, message: 'Paystack secret key is not configured' },
        { status: 500 }
      );
    }

    // Call the Paystack API to verify the transaction
    const response = await axios.get(
      `${PAYSTACK_API_URL}/transaction/verify/${reference}`,
      {
        headers: {
          Authorization: `Bearer ${secretKey}`,
          'Content-Type': 'application/json',
        },
      }
    );

    // Return the response from Paystack
    return NextResponse.json(response.data);
  } catch (error: any) {
    logger.error('Error verifying transaction:', error.response?.data || error.message);
    return NextResponse.json(
      {
        status: false,
        message: 'Failed to verify transaction',
        error: error.response?.data || error.message,
      },
      { status: 500 }
    );
  }
} 