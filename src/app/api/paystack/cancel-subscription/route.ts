import { NextResponse } from 'next/server';
import axios from 'axios';
import { logger } from '@/lib/logger';

// Paystack API base URL
const PAYSTACK_API_URL = 'https://api.paystack.co';

export async function POST(request: Request) {
  try {
    // Get the secret key from environment variables
    const secretKey = process.env.PAYSTACK_SECRET_KEY;
    if (!secretKey) {
      logger.error('Paystack secret key is not configured');
      return NextResponse.json(
        { status: false, message: 'Paystack secret key is not configured' },
        { status: 500 }
      );
    }

    // Parse the request body
    const body = await request.json();
    const { code, token } = body;

    if (!code || !token) {
      logger.error('Missing required parameters:', { code: !!code, token: !!token });
      return NextResponse.json(
        { status: false, message: 'Subscription code and email token are required' },
        { status: 400 }
      );
    }

    logger.log(`Attempting to cancel subscription with code: ${code} and token: ${token}`);

    // Call the Paystack API to cancel the subscription
    try {
      logger.log('Sending cancel request to Paystack API...');
      const response = await axios.post(
        `${PAYSTACK_API_URL}/subscription/disable`,
        {
          code,
          token,
        },
        {
          headers: {
            Authorization: `Bearer ${secretKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      logger.log('Paystack API response:', JSON.stringify(response.data, null, 2));
      
      if (response.data.status) {
        logger.log('Subscription canceled successfully.');
      } else {
        logger.error('Paystack reported error in response:', response.data);
      }
      
      // Return the response from Paystack
      return NextResponse.json(response.data);
    } catch (apiError: any) {
      logger.error('Paystack API error details:', {
        status: apiError.response?.status,
        statusText: apiError.response?.statusText,
        data: apiError.response?.data,
        message: apiError.message
      });
      
      return NextResponse.json(
        {
          status: false,
          message: 'Failed to cancel subscription with Paystack',
          error: apiError.response?.data || apiError.message,
        },
        { status: apiError.response?.status || 500 }
      );
    }
  } catch (error: any) {
    logger.error('Error canceling subscription:', error.message);
    return NextResponse.json(
      {
        status: false,
        message: 'Failed to cancel subscription',
        error: error.message,
      },
      { status: 500 }
    );
  }
} 