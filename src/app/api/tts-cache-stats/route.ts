import { NextRequest, NextResponse } from 'next/server';
import { list } from '@vercel/blob';
import { logger } from '@/lib/logger';

// Cache configuration
const CACHE_PREFIX = 'tts-cache';
const CACHE_EXPIRY_DAYS = 30;

interface CacheStatsResponse {
  success: boolean;
  totalFiles?: number;
  totalSize?: number;
  expiredFiles?: number;
  expiredSize?: number;
  cacheExpiryDays?: number;
  error?: string;
  timestamp?: string;
}

/**
 * Get TTS cache statistics
 * This endpoint provides information about the current state of the TTS cache
 */
export async function GET(request: NextRequest): Promise<NextResponse<CacheStatsResponse>> {
  try {
    logger.log('[TTS Cache Stats] Getting cache statistics');

    // List all blobs with the TTS cache prefix
    const { blobs } = await list({
      prefix: CACHE_PREFIX,
      limit: 1000 // Adjust based on expected cache size
    });

    const now = new Date();
    const expiryThreshold = new Date(now.getTime() - (CACHE_EXPIRY_DAYS * 24 * 60 * 60 * 1000));

    let totalSize = 0;
    let expiredCount = 0;
    let expiredSize = 0;

    for (const blob of blobs) {
      totalSize += blob.size;
      
      const uploadedAt = new Date(blob.uploadedAt);
      if (uploadedAt < expiryThreshold) {
        expiredCount++;
        expiredSize += blob.size;
      }
    }

    const stats = {
      success: true,
      totalFiles: blobs.length,
      totalSize,
      expiredFiles: expiredCount,
      expiredSize,
      cacheExpiryDays: CACHE_EXPIRY_DAYS,
      timestamp: new Date().toISOString()
    };

    logger.log('[TTS Cache Stats] Cache statistics:', stats);

    return NextResponse.json(stats);

  } catch (error) {
    logger.error('[TTS Cache Stats] Error getting cache stats:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get cache stats',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
