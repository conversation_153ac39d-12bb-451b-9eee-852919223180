import { NextRequest, NextResponse } from 'next/server';
import { list, del } from '@vercel/blob';
import { logger } from '@/lib/logger';

// Cache configuration
const CACHE_PREFIX = 'tts-cache';
const CACHE_EXPIRY_DAYS = 30;

interface CacheCleanupResponse {
  success: boolean;
  deletedCount?: number;
  totalSize?: number;
  totalFiles?: number;
  error?: string;
  timestamp?: string;
}

/**
 * Cron job endpoint for automatic TTS cache cleanup
 * This endpoint is called by Vercel Cron Jobs every 3 days at 2:00 AM UTC
 * Schedule: "0 2 star-slash-3 star star" (every 3 days at 2:00 AM)
 */
export async function GET(request: NextRequest): Promise<NextResponse<CacheCleanupResponse>> {
  const startTime = Date.now();

  try {
    logger.log('[TTS Cache Cron] Starting automated cache cleanup');

    // Verify this is a legitimate cron job request from Vercel
    const userAgent = request.headers.get('user-agent');
    if (!userAgent?.includes('vercel-cron')) {
      logger.log('[TTS Cache Cron] Unauthorized request - not from Vercel cron');
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized - this endpoint is only accessible via Vercel Cron Jobs',
          timestamp: new Date().toISOString()
        },
        { status: 401 }
      );
    }

    // List all blobs with the TTS cache prefix
    logger.log('[TTS Cache Cron] Fetching cached files...');
    const { blobs } = await list({
      prefix: CACHE_PREFIX,
      limit: 1000 // Adjust based on expected cache size
    });

    logger.log(`[TTS Cache Cron] Found ${blobs.length} cached files`);

    const now = new Date();
    const expiryThreshold = new Date(now.getTime() - (CACHE_EXPIRY_DAYS * 24 * 60 * 60 * 1000));

    let deletedCount = 0;
    let totalSize = 0;
    let deletedSize = 0;
    const errors: string[] = [];

    // Calculate total size and identify expired files
    for (const blob of blobs) {
      totalSize += blob.size;
    }

    // Check each blob and delete ALL for testing (normally would check expiry)
    for (const blob of blobs) {
      const uploadedAt = new Date(blob.uploadedAt);

      // For testing: delete all files, not just expired ones
      // if (uploadedAt < expiryThreshold) {
        try {
          await del(blob.url);
          deletedCount++;
          deletedSize += blob.size;
          logger.log(`[TTS Cache Cron] Deleted cache file: ${blob.pathname} (${blob.size} bytes)`);
        } catch (deleteError) {
          const errorMsg = `Failed to delete ${blob.pathname}: ${deleteError instanceof Error ? deleteError.message : 'Unknown error'}`;
          logger.error(`[TTS Cache Cron] ${errorMsg}`);
          errors.push(errorMsg);
        }
      // }
    }

    const duration = Date.now() - startTime;
    const result = {
      success: true,
      deletedCount,
      totalSize: deletedSize,
      totalFiles: blobs.length,
      timestamp: new Date().toISOString()
    };

    logger.log('[TTS Cache Cron] Cleanup completed:', {
      ...result,
      duration: `${duration}ms`,
      remainingFiles: blobs.length - deletedCount,
      remainingSize: totalSize - deletedSize,
      errors: errors.length
    });

    // If there were errors but some deletions succeeded, still return success
    if (errors.length > 0) {
      logger.warn(`[TTS Cache Cron] Completed with ${errors.length} errors:`, errors);
    }

    return NextResponse.json(result);

  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error('[TTS Cache Cron] Error during cleanup:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Cache cleanup failed',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

/**
 * Manual trigger endpoint (for testing purposes)
 * This allows manual triggering of the cleanup process
 */
export async function POST(request: NextRequest): Promise<NextResponse<CacheCleanupResponse>> {
  try {
    logger.log('[TTS Cache Cron] Manual cleanup triggered');

    // For manual triggers, we can add additional security if needed
    // For now, we'll allow it for testing purposes

    // Reuse the GET logic by creating a new request
    return await GET(request);

  } catch (error) {
    logger.error('[TTS Cache Cron] Error in manual cleanup:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Manual cleanup failed',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
