import { MetadataRoute } from 'next'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://www.ai-flashcards.co.za/'

  // Define your static routes
  const routes = [
    '',
    '/auth/signin',
    '/auth/signup',
    '/auth/forgot-password',
    '/settings',
    '/subscription',
    '/history',
    '/legal/privacy',
    '/legal/terms',
  ]

  return routes.map(route => ({
    url: `${baseUrl}${route}`,
    lastModified: new Date(),
    changeFrequency: 'daily',
    priority: route === '' ? 1 : 0.8,
  }))
}