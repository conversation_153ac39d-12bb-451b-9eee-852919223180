/** @type {import('next').NextConfig} */
const nextConfig = {
  /* config options here */
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'placehold.co',
        port: '',
        pathname: '/**',
      },
    ],
  },
  experimental: {
    serverActions: {
      bodySizeLimit: '20mb', // 20MB limit to account for Base64 encoding overhead
    },
  },
  env: {
    // Make environment variables available to the client
    GEMINI_MODEL: process.env.GEMINI_MODEL || 'googleai/gemini-2.0-flash',
  },
};

module.exports = nextConfig; 