# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- **Development server**: `npm run dev` (runs on port 9002 with Turbopack)
- **Build**: `npm run build`
- **Lint**: `npm run lint`
- **Type checking**: `npm run typecheck`
- **AI development**: `npm run genkit:dev` or `npm run genkit:watch` (for Genkit AI flows)

## Architecture Overview

This is a Next.js 15 flashcard generation application that combines AI-powered content creation with advanced text-to-speech functionality.

### Core Technology Stack
- **Frontend**: Next.js 15, React 18, Tai<PERSON><PERSON> CSS, Radix UI components
- **AI Integration**: Google Genkit framework with Gemini 2.0 Flash model
- **Text-to-Speech**: ElevenLabs Flash v2.5 model with 15 voice options
- **Authentication & Storage**: Firebase Auth + Firestore
- **File Storage**: Vercel Blob (for uploads and TTS cache)
- **Payments**: Paystack integration for subscription tiers

### Key Architectural Patterns

#### AI Flow Architecture (`src/ai/`)
- Uses Google Genkit framework for AI integrations
- Two main flows: `generate-flashcards.ts` (from documents) and `generate-topic-flashcards.ts` (from topics)
- Server actions in `src/app/actions.ts` provide the interface between frontend and AI flows
- Prompts are modularized in `src/ai/prompts/`

#### TTS with Sophisticated Caching (`src/lib/elevenlabs-tts.ts`, `src/lib/tts-cache.ts`)
- **ElevenLabs Integration**: Uses Flash v2.5 model for 75ms latency speech generation
- **Intelligent Caching**: Deterministic cache keys based on text content + voice selection
- **Cache Management**: 30-day expiry with automated cleanup via Vercel Cron Jobs (`/api/cron/tts-cache-cleanup`)
- **Performance**: Cache-first approach with fallback to real-time generation
- **Security**: Filters sensitive content patterns before caching

#### Subscription & Usage System
- **Tiers**: FREE, BASIC, PRO with different limits
- **Payment Integration**: Paystack for subscription management
- **Usage Tracking**: Per-user limits and usage monitoring in `src/lib/usage-service.ts`

#### Component Architecture
- **Flashcard Display**: `FlashcardViewer.tsx` handles card flipping and navigation
- **TTS Components**: `SpeakerButton.tsx`, `LazyTTSButton.tsx`, `PrerecordedSpeakerButton.tsx`
- **File Handling**: `FileUploadForm.tsx`, `PdfUploadForm.tsx` for document processing
- **Export**: `ExportButton.tsx` supports PDF and JSON export formats
- **Virtualization**: `VirtualizedFlashcardList.tsx` for large flashcard sets

### Data Flow

1. **File Upload**: Documents uploaded to Vercel Blob storage
2. **AI Processing**: Genkit flows process content with Gemini 2.0 Flash
3. **Storage**: Generated flashcards saved to Firestore with user association
4. **TTS Generation**: Text converted to speech via ElevenLabs with cache-first strategy
5. **Export**: Flashcards can be exported as PDF (using pdf-lib + Puppeteer) or JSON

### Service Layers

#### Authentication (`src/lib/auth-context.tsx`)
- Firebase Auth integration with email/password and Google OAuth
- User context provider for React components

#### Flashcard Service (`src/lib/flashcard-service.ts`)
- CRUD operations for flashcard sets in Firestore
- Duplicate detection and user ownership validation
- React hooks for authenticated operations

#### Settings Management (`src/lib/settings-service.ts`)
- User preferences: answer detail level, card count ranges, voice selection
- Persisted in Firestore per user

### Environment Variables Required

```bash
# AI & TTS
GEMINI_API_KEY=your_google_ai_api_key
ELEVENLABS_API_KEY=your_elevenlabs_api_key

# Storage
BLOB_READ_WRITE_TOKEN=your_vercel_blob_token

# Firebase (multiple vars - see .env.example)
NEXT_PUBLIC_FIREBASE_API_KEY=...
NEXT_PUBLIC_FIREBASE_PROJECT_ID=...
# ... other Firebase config

# Payments
PAYSTACK_SECRET_KEY=your_paystack_secret
NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=your_paystack_public
```

### Testing & Performance

- **TTS Cache Testing**: `scripts/test-tts-cache.js` validates caching behavior
- **Performance Monitoring**: `PerformanceMonitor.tsx` tracks component render times
- **Puppeteer Integration**: Used for PDF generation with Chromium in serverless environment

### Security Considerations

- API keys handled server-side only (Genkit flows, ElevenLabs integration)
- Input validation throughout (Zod schemas in types)
- Firebase security rules for user data isolation
- Content filtering in TTS cache to prevent sensitive data storage
- Rate limiting and usage quotas per subscription tier

### PWA Features

- Service worker implementation (`public/sw.js`)
- iOS keyboard fixes and safe area support
- Offline functionality considerations
- App manifest with proper icons and metadata