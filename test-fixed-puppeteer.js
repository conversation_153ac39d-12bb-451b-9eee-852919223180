// Test script to verify the fixed Puppeteer configuration
const testFlashcards = [
  {
    question: "Test question 1",
    answer: "Test answer 1"
  },
  {
    question: "Test question 2", 
    answer: "Test answer 2"
  }
];

async function testFixedPuppeteer() {
  try {
    console.log('Testing fixed Puppeteer configuration...');
    
    const response = await fetch('http://localhost:9002/api/export-pdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        flashcards: testFlashcards,
        title: 'Fixed Puppeteer Test'
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`HTTP ${response.status}: ${errorData.error || 'Unknown error'}`);
    }

    console.log('✅ PDF export successful!');
    
    const blob = await response.blob();
    console.log('📄 PDF size:', blob.size, 'bytes');
    console.log('🔧 Puppeteer configuration fixed!');

  } catch (error) {
    console.error('❌ PDF export failed:', error.message);
  }
}

testFixedPuppeteer();
