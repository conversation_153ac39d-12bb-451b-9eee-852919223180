# **App Name**: Flashcard AI

## Core Features:

- PDF Upload: Allow users to upload a PDF document from their local machine.
- Flashcard Generation: Utilize a generative AI model (Gemini) as a tool, to intelligently extract key concepts, definitions, and important facts from the uploaded PDF content and generate flashcards.
- Interactive Flashcard Display: Present the generated flashcards in an interactive and engaging manner, including an intuitive 'flip' animation/transition on click or tap to reveal the answer.
- Flashcard Export: Allow users to export generated flashcards as downloadable JSON file.

## Style Guidelines:

- Primary color: Warm beige (#F5F5DC) to create a cozy and inviting learning environment.
- Background color: Off-white (#FAFAFA), offering a soft and neutral backdrop that reduces eye strain.
- Accent color: Muted gold (#BDB76B) for interactive elements and important actions, adding a touch of sophistication.
- Clean and readable sans-serif font for optimal readability of both questions and answers.
- Use simple, clear icons to represent actions like upload, download, and settings.
- Ensure a clean and intuitive layout with a focus on the flashcard display area.
- Employ a subtle, smooth animation for the card flipping effect.