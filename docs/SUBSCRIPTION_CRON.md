# Subscription Expiration Cron Job

This document explains the subscription expiration handling system implemented for the cancel-at-period-end functionality.

## Overview

When users cancel their subscriptions, they now retain access to premium features until their subscription's natural expiration date. To ensure users are properly downgraded when their subscriptions expire, we have implemented both webhook handlers and a backup cron job system.

## Components

### 1. Webhook Handlers (`src/app/api/paystack/webhook/route.ts`)

The webhook system handles real-time subscription status changes:

- **`subscription.disable`**: Marks subscription as cancelled but keeps current tier
- **`subscription.not_renewing`**: Marks subscription as non-renewing but keeps current tier  
- **`subscription.expired`**: Actually downgrades user to FREE tier when subscription expires

### 2. Cron Job (`src/app/api/cron/check-expired-subscriptions/route.ts`)

A backup system that periodically checks for expired subscriptions that may have been missed by webhooks:

- Queries all users with paid tiers (BASIC/PRO)
- Checks their current subscription status with Paystack
- Downgrades users whose subscriptions have expired
- Provides detailed logging and error reporting

## Setup

### Environment Variables

Add to your `.env` file:

```bash
# Optional: Secret for authenticating cron job requests
CRON_SECRET=your-secure-random-string-here
```

### Deployment Options

#### Option 1: Vercel Cron Jobs

If deploying on Vercel, create a `vercel.json` file:

```json
{
  "crons": [
    {
      "path": "/api/cron/check-expired-subscriptions",
      "schedule": "0 2 * * *"
    }
  ]
}
```

This runs the check daily at 2 AM UTC.

#### Option 2: External Cron Service

Use services like:
- GitHub Actions (scheduled workflows)
- Uptime Robot (monitoring with webhooks)
- Cron-job.org
- Your server's crontab

Example curl command:
```bash
curl -X POST https://your-domain.com/api/cron/check-expired-subscriptions \
  -H "Authorization: Bearer your-cron-secret"
```

#### Option 3: Manual Testing

For testing, you can call the endpoint manually:

```bash
# GET request to see endpoint info
curl https://your-domain.com/api/cron/check-expired-subscriptions

# POST request to run the check
curl -X POST https://your-domain.com/api/cron/check-expired-subscriptions \
  -H "Authorization: Bearer your-cron-secret"
```

## Response Format

The cron job returns a JSON response:

```json
{
  "success": true,
  "processed": 15,
  "downgraded": 2,
  "errors": ["Error processing user xyz: ..."]
}
```

## Monitoring

Monitor the cron job execution through:

1. **Application logs**: Check for `[CRON]` prefixed log entries
2. **Response data**: Track processed/downgraded counts
3. **Error reporting**: Monitor the `errors` array in responses
4. **User complaints**: Users reporting unexpected access loss

## Recommended Schedule

- **Production**: Daily at 2 AM UTC (`0 2 * * *`)
- **Development**: Weekly or manual execution
- **High-volume**: Every 6 hours if needed

## Security Considerations

1. **Authentication**: Use the `CRON_SECRET` environment variable
2. **Rate limiting**: Consider implementing rate limiting for the endpoint
3. **Logging**: Ensure sensitive data (emails, subscription codes) are properly logged
4. **Error handling**: Graceful handling of Paystack API failures

## Troubleshooting

### Common Issues

1. **Users not downgraded**: Check webhook configuration and cron job execution
2. **False downgrades**: Verify Paystack subscription status accuracy
3. **Performance issues**: Consider batching for large user bases
4. **API rate limits**: Implement proper delays between Paystack API calls

### Debug Steps

1. Check application logs for `[CRON]` entries
2. Manually test the endpoint with a small user set
3. Verify Paystack webhook delivery
4. Check Firebase user data consistency
