#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * <PERSON>ript to process voices.json and extract specific fields
 * 
 * Input: docs/voices.json
 * Output: Filtered JSON with only voice_id, name, labels, and preview_url
 */

function processVoicesFile() {
    try {
        // Define input file path
        const inputFilePath = path.join(__dirname, 'docs', 'voices.json');
        
        // Check if input file exists
        if (!fs.existsSync(inputFilePath)) {
            console.error(`Error: Input file not found at ${inputFilePath}`);
            process.exit(1);
        }

        // Read and parse the JSON file
        console.log(`Reading file: ${inputFilePath}`);
        const rawData = fs.readFileSync(inputFilePath, 'utf8');
        
        let inputData;
        try {
            inputData = JSON.parse(rawData);
        } catch (parseError) {
            console.error('Error: Invalid JSON format in input file');
            console.error(parseError.message);
            process.exit(1);
        }

        // Validate input structure
        if (!inputData.voices || !Array.isArray(inputData.voices)) {
            console.error('Error: Input file does not contain a valid "voices" array');
            process.exit(1);
        }

        // Process voices and extract required fields
        const processedVoices = inputData.voices.map(voice => {
            // Extract only the required fields
            const processedVoice = {
                voice_id: voice.voice_id,
                name: voice.name,
                labels: voice.labels,
                preview_url: voice.preview_url
            };

            return processedVoice;
        }).filter(voice => {
            // Filter out voices that are missing required fields
            return voice.voice_id && voice.name && voice.labels && voice.preview_url;
        });

        // Create output structure
        const outputData = {
            voices: processedVoices
        };

        // Output the result as formatted JSON to stdout
        console.log(JSON.stringify(outputData, null, 2));

        // Also provide summary information to stderr so it doesn't interfere with JSON output
        console.error(`\nProcessing complete:`);
        console.error(`- Input voices: ${inputData.voices.length}`);
        console.error(`- Output voices: ${processedVoices.length}`);
        console.error(`- Filtered out: ${inputData.voices.length - processedVoices.length} voices (missing required fields)`);

    } catch (error) {
        console.error('Error processing voices file:');
        console.error(error.message);
        process.exit(1);
    }
}

// Function to save output to file instead of stdout
function saveToFile(outputPath) {
    try {
        // Define input file path
        const inputFilePath = path.join(__dirname, 'docs', 'voices.json');
        
        // Check if input file exists
        if (!fs.existsSync(inputFilePath)) {
            console.error(`Error: Input file not found at ${inputFilePath}`);
            process.exit(1);
        }

        // Read and parse the JSON file
        const rawData = fs.readFileSync(inputFilePath, 'utf8');
        const inputData = JSON.parse(rawData);

        // Validate input structure
        if (!inputData.voices || !Array.isArray(inputData.voices)) {
            console.error('Error: Input file does not contain a valid "voices" array');
            process.exit(1);
        }

        // Process voices
        const processedVoices = inputData.voices.map(voice => ({
            voice_id: voice.voice_id,
            name: voice.name,
            labels: voice.labels,
            preview_url: voice.preview_url
        })).filter(voice => {
            return voice.voice_id && voice.name && voice.labels && voice.preview_url;
        });

        // Create output structure
        const outputData = {
            voices: processedVoices
        };

        // Write to file
        fs.writeFileSync(outputPath, JSON.stringify(outputData, null, 2), 'utf8');
        
        console.log(`Processing complete:`);
        console.log(`- Input file: ${inputFilePath}`);
        console.log(`- Output file: ${outputPath}`);
        console.log(`- Input voices: ${inputData.voices.length}`);
        console.log(`- Output voices: ${processedVoices.length}`);
        console.log(`- Filtered out: ${inputData.voices.length - processedVoices.length} voices (missing required fields)`);

    } catch (error) {
        console.error('Error processing voices file:');
        console.error(error.message);
        process.exit(1);
    }
}

// Main execution
function main() {
    const args = process.argv.slice(2);
    
    // Check for help flag
    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
Voice Processor Script

Usage:
  node process-voices.js                    # Output JSON to stdout
  node process-voices.js --output <file>    # Save JSON to specified file
  node process-voices.js -o <file>          # Save JSON to specified file (short form)
  node process-voices.js --help             # Show this help message

Description:
  Processes docs/voices.json and extracts only the required fields:
  - voice_id
  - name  
  - labels (with all nested properties)
  - preview_url

Examples:
  node process-voices.js > filtered-voices.json
  node process-voices.js --output processed-voices.json
  node process-voices.js -o voices-filtered.json
        `);
        return;
    }

    // Check for output file option
    const outputIndex = args.findIndex(arg => arg === '--output' || arg === '-o');
    if (outputIndex !== -1 && args[outputIndex + 1]) {
        const outputPath = args[outputIndex + 1];
        saveToFile(outputPath);
    } else {
        // Default: output to stdout
        processVoicesFile();
    }
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = { processVoicesFile, saveToFile };
