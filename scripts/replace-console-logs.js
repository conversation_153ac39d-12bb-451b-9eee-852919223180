#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to replace console.log statements with logger calls
 * This script will update all remaining files that contain console statements
 */

const fs = require('fs');
const path = require('path');

// Files that have already been updated manually
const UPDATED_FILES = [
  'src/lib/firebase-admin.ts',
  'src/lib/tts-cache.ts',
  'src/lib/elevenlabs-tts.ts',
  'src/lib/settings-service.ts',
  'src/app/api/text-to-speech/route.ts',
  'src/app/api/settings/route.ts',
  'src/app/api/text-to-speech-fallback/route.ts',
  'src/app/api/tts-cache-stats/route.ts',
  'src/app/api/cron/tts-cache-cleanup/route.ts',
  'src/app/api/paystack/debug/route.ts',
  'src/app/api/paystack/initialize-transaction/route.ts',
  'src/app/api/paystack/webhook/route.ts',
  'src/app/api/blob-upload/route.ts',
  'src/app/api/blob-delete/route.ts',
  'src/app/api/blob-list/route.ts',
  'src/app/api/export-pdf/route.ts',
  'src/components/FileUploadForm.tsx',
  'src/components/PdfUploadForm.tsx'
];

// Files to update (additional batch)
const FILES_TO_UPDATE = [
  'src/lib/api-key-utils.ts',
  'src/lib/auth-context.tsx',
  'src/lib/settings-service-server.ts',
  'src/lib/tts-voices.ts',
  'src/lib/flashcard-service.ts',
  'src/lib/paystack-service.ts',
  'src/lib/subscription-context.tsx',
  'src/app/actions.ts',
  'src/app/page.tsx',
  'src/app/history/page.tsx',
  'src/app/api/paystack/cancel-subscription/route.ts',
  'src/app/api/paystack/verify-transaction/[reference]/route.ts',
  'src/app/api/paystack/subscription/[email]/route.ts',
  'src/app/subscription/callback/page.tsx',
  'src/app/settings/page.tsx',
  'src/hooks/use-prerecorded-audio.ts',
  'src/hooks/use-audio.ts',
  'src/hooks/use-tts-voices.ts',
  'src/components/TopicInputForm.tsx',
  'src/components/ElevenLabsApiKeySettings.tsx',
  'src/components/ExportButton.tsx',
  'src/components/ui/TierManagement.tsx',
  'src/components/FlashcardSetClient.tsx',
  'src/ai/flows/generate-topic-flashcards.ts',
  'src/ai/flows/generate-flashcards.ts'
];

function addLoggerImport(content, filePath) {
  // Check if logger import already exists
  if (content.includes("import { logger } from")) {
    return content;
  }

  // Find the last import statement
  const lines = content.split('\n');
  let lastImportIndex = -1;
  
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].trim().startsWith('import ') && !lines[i].includes('type')) {
      lastImportIndex = i;
    }
  }

  if (lastImportIndex === -1) {
    // No imports found, add at the top
    return `import { logger } from '@/lib/logger';\n\n${content}`;
  }

  // Add logger import after the last import
  lines.splice(lastImportIndex + 1, 0, "import { logger } from '@/lib/logger';");
  return lines.join('\n');
}

function replaceConsoleStatements(content) {
  // Replace console.log with logger.log
  content = content.replace(/console\.log\(/g, 'logger.log(');
  
  // Replace console.error with logger.error
  content = content.replace(/console\.error\(/g, 'logger.error(');
  
  // Replace console.warn with logger.warn
  content = content.replace(/console\.warn\(/g, 'logger.warn(');
  
  // Replace console.info with logger.info
  content = content.replace(/console\.info\(/g, 'logger.info(');
  
  return content;
}

function updateFile(filePath) {
  try {
    console.log(`Updating ${filePath}...`);
    
    if (!fs.existsSync(filePath)) {
      console.log(`  ⚠️  File not found: ${filePath}`);
      return;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check if file contains console statements
    if (!content.match(/console\.(log|error|warn|info)/)) {
      console.log(`  ✅ No console statements found in ${filePath}`);
      return;
    }

    // Add logger import
    let updatedContent = addLoggerImport(content, filePath);
    
    // Replace console statements
    updatedContent = replaceConsoleStatements(updatedContent);
    
    // Write back to file
    fs.writeFileSync(filePath, updatedContent, 'utf8');
    console.log(`  ✅ Updated ${filePath}`);
    
  } catch (error) {
    console.error(`  ❌ Error updating ${filePath}:`, error.message);
  }
}

function main() {
  console.log('🔧 Replacing console statements with logger calls...\n');
  
  FILES_TO_UPDATE.forEach(updateFile);
  
  console.log('\n✅ Console statement replacement completed!');
  console.log('\nFiles updated:');
  FILES_TO_UPDATE.forEach(file => console.log(`  - ${file}`));
  
  console.log('\nFiles already updated manually:');
  UPDATED_FILES.forEach(file => console.log(`  - ${file}`));
}

if (require.main === module) {
  main();
}
