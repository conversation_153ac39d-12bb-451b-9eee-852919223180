#!/usr/bin/env node

/**
 * SEO and Manifest Testing Script
 * Tests all SEO-related functionality and manifest accessibility
 */

const fs = require('fs');
const path = require('path');

function testManifestFile() {
  console.log('🔍 Testing Manifest Configuration...\n');

  const manifestPath = path.join(__dirname, '..', 'src', 'app', 'manifest.ts');
  
  if (fs.existsSync(manifestPath)) {
    const manifestContent = fs.readFileSync(manifestPath, 'utf8');
    
    console.log('✅ Manifest file exists at src/app/manifest.ts');
    
    // Check for key manifest properties
    const checks = [
      { property: 'name:', description: 'App name' },
      { property: 'short_name:', description: 'Short name' },
      { property: 'description:', description: 'App description' },
      { property: 'start_url:', description: 'Start URL' },
      { property: 'display:', description: 'Display mode' },
      { property: 'background_color:', description: 'Background color' },
      { property: 'theme_color:', description: 'Theme color' },
      { property: 'icons:', description: 'App icons' },
      { property: 'screenshots:', description: 'App screenshots' },
      { property: 'categories:', description: 'App categories' },
    ];

    checks.forEach(check => {
      if (manifestContent.includes(check.property)) {
        console.log(`  ✅ ${check.description} configured`);
      } else {
        console.log(`  ⚠️  ${check.description} missing`);
      }
    });

    // Check for current features in description
    const currentFeatures = [
      'text-to-speech',
      'document upload',
      'PDF',
      'Word',
      'PowerPoint',
      'export',
      'AI-powered'
    ];

    console.log('\n📋 Feature mentions in manifest:');
    currentFeatures.forEach(feature => {
      if (manifestContent.toLowerCase().includes(feature.toLowerCase())) {
        console.log(`  ✅ ${feature} mentioned`);
      } else {
        console.log(`  ❌ ${feature} not mentioned`);
      }
    });

  } else {
    console.log('❌ Manifest file not found');
  }
}

function testMetadataFile() {
  console.log('\n🔍 Testing Metadata Configuration...\n');

  const metadataPath = path.join(__dirname, '..', 'src', 'app', 'metadata.ts');
  
  if (fs.existsSync(metadataPath)) {
    const metadataContent = fs.readFileSync(metadataPath, 'utf8');
    
    console.log('✅ Metadata file exists at src/app/metadata.ts');
    
    // Check for key metadata properties
    const checks = [
      { property: 'title:', description: 'Page title' },
      { property: 'description:', description: 'Meta description' },
      { property: 'keywords:', description: 'SEO keywords' },
      { property: 'openGraph:', description: 'Open Graph tags' },
      { property: 'twitter:', description: 'Twitter Card tags' },
      { property: 'icons:', description: 'Favicon configuration' },
      { property: 'robots:', description: 'Robots meta tag' },
    ];

    checks.forEach(check => {
      if (metadataContent.includes(check.property)) {
        console.log(`  ✅ ${check.description} configured`);
      } else {
        console.log(`  ⚠️  ${check.description} missing`);
      }
    });

    // Check for current features in keywords
    const currentKeywords = [
      'text-to-speech',
      'TTS',
      'ElevenLabs',
      'voice flashcards',
      'audio learning',
      'PDF export',
      'JSON export',
      'subscription',
      'topic generation'
    ];

    console.log('\n📋 Current feature keywords:');
    currentKeywords.forEach(keyword => {
      if (metadataContent.toLowerCase().includes(keyword.toLowerCase())) {
        console.log(`  ✅ ${keyword} included`);
      } else {
        console.log(`  ❌ ${keyword} missing`);
      }
    });

  } else {
    console.log('❌ Metadata file not found');
  }
}

function testLayoutConfiguration() {
  console.log('\n🔍 Testing Layout Configuration...\n');

  const layoutPath = path.join(__dirname, '..', 'src', 'app', 'layout.tsx');
  
  if (fs.existsSync(layoutPath)) {
    const layoutContent = fs.readFileSync(layoutPath, 'utf8');
    
    console.log('✅ Root layout file exists');
    
    // Check for manifest link
    if (layoutContent.includes('manifest.webmanifest')) {
      console.log('  ✅ Manifest linked correctly (/manifest.webmanifest)');
    } else if (layoutContent.includes('manifest.json')) {
      console.log('  ⚠️  Manifest uses old path (/manifest.json) - should be /manifest.webmanifest');
    } else {
      console.log('  ❌ Manifest link missing');
    }

    // Check for apple-touch-icon
    if (layoutContent.includes('apple-touch-icon')) {
      console.log('  ✅ Apple touch icon configured');
    } else {
      console.log('  ❌ Apple touch icon missing');
    }

    // Check for JSON-LD
    if (layoutContent.includes('JsonLd')) {
      console.log('  ✅ JSON-LD structured data included');
    } else {
      console.log('  ❌ JSON-LD structured data missing');
    }

  } else {
    console.log('❌ Root layout file not found');
  }
}

function testRequiredAssets() {
  console.log('\n🔍 Testing Required Assets...\n');

  const publicDir = path.join(__dirname, '..', 'public');
  
  const requiredAssets = [
    { file: 'favicon.ico', description: 'Favicon (ICO format)' },
    { file: 'apple-touch-icon.png', description: 'Apple touch icon (180x180)' },
    { file: 'og-image.png', description: 'Open Graph image (1200x630)' },
    { file: 'icon-192x192.png', description: 'PWA icon 192x192' },
    { file: 'icon-512x512.png', description: 'PWA icon 512x512' },
    { file: 'robots.txt', description: 'Robots.txt file' },
  ];

  requiredAssets.forEach(asset => {
    const assetPath = path.join(publicDir, asset.file);
    if (fs.existsSync(assetPath)) {
      console.log(`  ✅ ${asset.description} exists`);
    } else {
      console.log(`  ❌ ${asset.description} missing - create ${asset.file}`);
    }
  });

  // Check for existing assets that can be used
  const existingAssets = [
    'flashcard_icon.png',
    'logo.png',
    'og-logo.png'
  ];

  console.log('\n📁 Existing assets that can be used:');
  existingAssets.forEach(asset => {
    const assetPath = path.join(publicDir, asset);
    if (fs.existsSync(assetPath)) {
      console.log(`  ✅ ${asset} available`);
    }
  });
}

function testSEOFiles() {
  console.log('\n🔍 Testing SEO Files...\n');

  const seoFiles = [
    { file: 'src/app/robots.ts', description: 'Robots.ts configuration' },
    { file: 'src/app/sitemap.ts', description: 'Sitemap.ts configuration' },
    { file: 'src/components/json-ld.tsx', description: 'JSON-LD components' },
  ];

  seoFiles.forEach(seoFile => {
    const filePath = path.join(__dirname, '..', seoFile.file);
    if (fs.existsSync(filePath)) {
      console.log(`  ✅ ${seoFile.description} exists`);
      
      // Check content for current features
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.includes('text-to-speech') || content.includes('TTS') || content.includes('ElevenLabs')) {
        console.log(`    ✅ Contains current feature references`);
      } else {
        console.log(`    ⚠️  May need current feature updates`);
      }
    } else {
      console.log(`  ❌ ${seoFile.description} missing`);
    }
  });
}

function testPageLayouts() {
  console.log('\n🔍 Testing Page-Specific Layouts...\n');

  const pageLayouts = [
    { path: 'src/app/settings/layout.tsx', description: 'Settings page layout' },
    { path: 'src/app/history/layout.tsx', description: 'History page layout' },
    { path: 'src/app/subscription/layout.tsx', description: 'Subscription page layout' },
    { path: 'src/app/auth/layout.tsx', description: 'Auth pages layout' },
  ];

  pageLayouts.forEach(layout => {
    const layoutPath = path.join(__dirname, '..', layout.path);
    if (fs.existsSync(layoutPath)) {
      console.log(`  ✅ ${layout.description} exists`);
    } else {
      console.log(`  ❌ ${layout.description} missing`);
    }
  });
}

function generateSummary() {
  console.log('\n📊 SEO & Manifest Audit Summary');
  console.log('================================\n');
  
  console.log('✅ Completed Tasks:');
  console.log('  - Fixed manifest.json → manifest.webmanifest path');
  console.log('  - Updated app descriptions with current features');
  console.log('  - Added comprehensive SEO keywords');
  console.log('  - Enhanced JSON-LD structured data');
  console.log('  - Created page-specific metadata layouts');
  console.log('  - Updated robots.ts and sitemap.ts');
  console.log('  - Fixed icon references in manifest');
  
  console.log('\n📋 Required Assets (you need to provide):');
  console.log('  1. favicon.ico - 16x16 or 32x32 ICO format');
  console.log('  2. apple-touch-icon.png - 180x180 PNG');
  console.log('  3. og-image.png - 1200x630 PNG for social sharing');
  console.log('  4. icon-192x192.png - 192x192 PNG for PWA');
  console.log('  5. icon-512x512.png - 512x512 PNG for PWA');
  
  console.log('\n🔧 Next Steps:');
  console.log('  1. Create and place the required image assets in public/');
  console.log('  2. Test manifest accessibility at /manifest.webmanifest');
  console.log('  3. Validate metadata with SEO tools');
  console.log('  4. Test PWA installation capabilities');
  console.log('  5. Verify Open Graph tags on social platforms');
  
  console.log('\n🎯 The 404 manifest error should now be resolved!');
}

function runAudit() {
  console.log('🔍 Flash Cards AI - SEO & Manifest Audit');
  console.log('=========================================\n');
  
  testManifestFile();
  testMetadataFile();
  testLayoutConfiguration();
  testRequiredAssets();
  testSEOFiles();
  testPageLayouts();
  generateSummary();
}

if (require.main === module) {
  runAudit();
}

module.exports = { runAudit };
