#!/usr/bin/env node

/**
 * Simple script to test TTS caching functionality
 * Run with: node scripts/test-tts-cache.js
 */

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:9002';

async function testTTSCache() {
  console.log('🧪 Testing TTS Cache Functionality');
  console.log('=====================================\n');

  const testText = "Hello, this is a test of the text-to-speech caching system.";
  const voiceName = "Kore";

  try {
    // First, test cache statistics (should work without authentication)
    console.log('📊 Test 1: Getting initial cache statistics...');
    try {
      const statsResponse = await fetch(`${BASE_URL}/api/tts-cache-stats`);
      const stats = await statsResponse.json();
      console.log('Initial cache stats:', {
        success: stats.success,
        totalFiles: stats.totalFiles,
        totalSize: stats.totalSize,
        expiredFiles: stats.expiredFiles
      });
    } catch (error) {
      console.log('Could not get initial cache stats:', error.message);
    }

    // Test 2: TTS request (will likely fail due to authentication, but that's expected)
    console.log('\n📝 Test 2: TTS request (authentication required)');
    const start1 = Date.now();

    const response1 = await fetch(`${BASE_URL}/api/text-to-speech`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: testText,
        voiceName: voiceName,
        userId: 'test-user-id' // This will likely fail authentication
      }),
    });

    const data1 = await response1.json();
    const duration1 = Date.now() - start1;

    console.log('TTS Response:', {
      success: data1.success,
      cached: data1.cached,
      hasAudioData: !!data1.audioData,
      hasAudioUrl: !!data1.audioUrl,
      duration: `${duration1}ms`,
      error: data1.error
    });

    if (!data1.success) {
      console.log('⚠️  TTS request failed (expected for test environment):', data1.error);
      console.log('💡 This is normal - TTS requires PRO user authentication');
    } else {
      console.log('✅ TTS request succeeded!');

      // If successful, test caching behavior
      console.log('\n📝 Test 3: Second TTS request (should use cache)');
      const start2 = Date.now();

      const response2 = await fetch(`${BASE_URL}/api/text-to-speech`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: testText,
          voiceName: voiceName,
          userId: 'test-user-id'
        }),
      });

      const data2 = await response2.json();
      const duration2 = Date.now() - start2;

      console.log('Second response:', {
        success: data2.success,
        cached: data2.cached,
        duration: `${duration2}ms`
      });

      if (data2.cached) {
        const speedup = ((duration1 - duration2) / duration1 * 100).toFixed(1);
        console.log(`🚀 Cache speedup: ${speedup}% faster`);
      }
    }

    // Test cache statistics
    console.log('\n📊 Getting cache statistics...');
    try {
      const statsResponse = await fetch(`${BASE_URL}/api/tts-cache-stats`);
      const stats = await statsResponse.json();
      console.log('Cache stats:', {
        success: stats.success,
        totalFiles: stats.totalFiles,
        totalSize: stats.totalSize,
        expiredFiles: stats.expiredFiles,
        expiredSize: stats.expiredSize,
        cacheExpiryDays: stats.cacheExpiryDays
      });
    } catch (error) {
      console.log('Could not get cache stats:', error.message);
    }

    // Test manual cache cleanup trigger
    console.log('\n🧹 Testing manual cache cleanup trigger...');
    try {
      const cleanupResponse = await fetch(`${BASE_URL}/api/cron/tts-cache-cleanup`, {
        method: 'POST'
      });
      const cleanupResult = await cleanupResponse.json();
      console.log('Cleanup result:', {
        success: cleanupResult.success,
        deletedCount: cleanupResult.deletedCount,
        totalFiles: cleanupResult.totalFiles,
        timestamp: cleanupResult.timestamp
      });
    } catch (error) {
      console.log('Could not trigger cleanup:', error.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testTTSCache().catch(console.error);
