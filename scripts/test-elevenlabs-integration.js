#!/usr/bin/env node

/**
 * Simple script to test ElevenLabs TTS integration
 * Run with: node scripts/test-elevenlabs-integration.js
 * 
 * Make sure to set ELEVENLABS_API_KEY in your .env file first
 */

const { config } = require('dotenv');
config();

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:9002';

async function testElevenLabsIntegration() {
  console.log('🧪 Testing ElevenLabs TTS Integration');
  console.log('=====================================\n');

  const testText = "Hello, this is a test of the ElevenLabs text-to-speech integration.";
  const voiceName = "Rachel";

  try {
    console.log('📝 Test Text:', testText);
    console.log('🎤 Voice:', voiceName);
    console.log('🌐 API URL:', `${BASE_URL}/api/text-to-speech`);
    console.log('\n⏳ Making TTS request...\n');

    const startTime = Date.now();

    const response = await fetch(`${BASE_URL}/api/text-to-speech`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: testText,
        voiceName: voiceName,
        userId: 'test-user'
      }),
    });

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log('📊 Response Status:', response.status);
    console.log('⏱️  Response Time:', `${duration}ms`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', errorText);
      return;
    }

    const data = await response.json();
    
    console.log('\n✅ Response Data:');
    console.log('  - Success:', data.success);
    console.log('  - Has Audio Data:', !!data.audioData);
    console.log('  - Has Audio URL:', !!data.audioUrl);
    console.log('  - Cached:', data.cached);
    
    if (data.audioData) {
      console.log('  - Audio Data Length:', data.audioData.length, 'characters (base64)');
      console.log('  - Estimated Audio Size:', Math.round(data.audioData.length * 0.75), 'bytes');
    }
    
    if (data.audioUrl) {
      console.log('  - Audio URL:', data.audioUrl);
    }

    if (data.error) {
      console.log('  - Error:', data.error);
    }

    // Test second request to check caching
    console.log('\n🔄 Testing cache functionality with second request...\n');
    
    const startTime2 = Date.now();
    const response2 = await fetch(`${BASE_URL}/api/text-to-speech`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: testText,
        voiceName: voiceName,
        userId: 'test-user'
      }),
    });
    const endTime2 = Date.now();
    const duration2 = endTime2 - startTime2;

    const data2 = await response2.json();
    
    console.log('📊 Second Request:');
    console.log('  - Response Time:', `${duration2}ms`);
    console.log('  - Cached:', data2.cached);
    console.log('  - Speed Improvement:', data2.cached ? `${Math.round(((duration - duration2) / duration) * 100)}%` : 'N/A');

    if (data.success && data2.success) {
      console.log('\n🎉 ElevenLabs TTS Integration Test PASSED!');
      console.log('✅ Audio generation working');
      console.log('✅ Caching system working');
      console.log('✅ API responses are valid');
    } else {
      console.log('\n❌ ElevenLabs TTS Integration Test FAILED!');
      console.log('❌ Check the error messages above');
    }

  } catch (error) {
    console.error('\n💥 Test Error:', error.message);
    console.error('❌ Make sure:');
    console.error('  1. The development server is running (npm run dev)');
    console.error('  2. ELEVENLABS_API_KEY is set in your .env file');
    console.error('  3. You have an active ElevenLabs subscription');
  }
}

// Test voice mapping
async function testVoiceMapping() {
  console.log('\n🎭 Testing Voice Mapping');
  console.log('========================\n');

  const voices = ['Rachel', 'Adam', 'George', 'Sarah', 'Domi'];
  
  for (const voice of voices) {
    try {
      console.log(`🎤 Testing voice: ${voice}`);
      
      const response = await fetch(`${BASE_URL}/api/text-to-speech`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: `This is a test of the ${voice} voice.`,
          voiceName: voice,
          userId: 'test-user'
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        console.log(`  ✅ ${voice} voice working`);
      } else {
        console.log(`  ❌ ${voice} voice failed:`, data.error);
      }
    } catch (error) {
      console.log(`  💥 ${voice} voice error:`, error.message);
    }
  }
}

// Run tests
async function runAllTests() {
  await testElevenLabsIntegration();
  await testVoiceMapping();
  
  console.log('\n🏁 All tests completed!');
}

if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { testElevenLabsIntegration, testVoiceMapping };
